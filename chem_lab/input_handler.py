"""
Input Handler Module

Converts molecular representations (SMILES, XYZ, SDF) to ASE atoms objects
with comprehensive validation and error handling.

Author: AI Chemistry Lab
License: MIT
"""

import os
import re
import numpy as np
from pathlib import Path
from typing import Union, List, Optional, Dict, Any
from ase import Atoms
from ase.io import read, write
from ase.data import atomic_numbers, chemical_symbols
import warnings

try:
    from rdkit import Chem
    from rdkit.Chem import AllChem, rdMolDescriptors
    RDKIT_AVAILABLE = True
except ImportError:
    RDKIT_AVAILABLE = False
    warnings.warn("RDKit not available. SMILES parsing will be limited.")

try:
    import openbabel as ob
    OPENBABEL_AVAILABLE = True
except ImportError:
    OPENBABEL_AVAILABLE = False
    warnings.warn("OpenBabel not available. Some format conversions may fail.")


class InputHandler:
    """
    Handles conversion of various molecular input formats to ASE Atoms objects.
    
    Supported formats:
    - SMILES strings
    - XYZ files
    - SDF files
    - PDB files
    - Gaussian input files
    - Direct coordinate input
    """
    
    def __init__(self):
        """Initialize the input handler."""
        self.supported_formats = ['.xyz', '.sdf', '.mol', '.pdb', '.gjf', '.com']
        
    def parse_molecule(self, input_data: Union[str, Path, List], 
                      format_hint: Optional[str] = None) -> Atoms:
        """
        Parse molecular input and return ASE Atoms object.
        
        Args:
            input_data: SMILES string, file path, or coordinate list
            format_hint: Optional format hint ('smiles', 'xyz', 'sdf', etc.)
            
        Returns:
            ASE Atoms object
            
        Raises:
            ValueError: If input cannot be parsed
            FileNotFoundError: If file path doesn't exist
        """
        
        # Determine input type
        if isinstance(input_data, (str, Path)):
            input_str = str(input_data)
            
            # Check if it's a file path
            if os.path.exists(input_str):
                return self._parse_file(input_str, format_hint)
            
            # Check if it's a SMILES string
            elif self._is_smiles(input_str):
                return self._parse_smiles(input_str)
            
            # Check if it's coordinate data
            elif self._is_coordinate_string(input_str):
                return self._parse_coordinate_string(input_str)
            
            else:
                raise ValueError(f"Cannot determine input type for: {input_str}")
                
        elif isinstance(input_data, list):
            return self._parse_coordinate_list(input_data)
        
        else:
            raise ValueError(f"Unsupported input type: {type(input_data)}")
    
    def _is_smiles(self, input_str: str) -> bool:
        """Check if string is a valid SMILES."""
        if not RDKIT_AVAILABLE:
            # Simple heuristic check
            return bool(re.match(r'^[A-Za-z0-9@+\-\[\]()=#$:.\\\/]+$', input_str))
        
        try:
            mol = Chem.MolFromSmiles(input_str)
            return mol is not None
        except:
            return False
    
    def _is_coordinate_string(self, input_str: str) -> bool:
        """Check if string contains coordinate data."""
        lines = input_str.strip().split('\n')
        if len(lines) < 2:
            return False
        
        # Look for pattern: Element X Y Z
        for line in lines:
            parts = line.strip().split()
            if len(parts) >= 4:
                try:
                    # Check if first part is element symbol
                    if parts[0] in chemical_symbols:
                        # Check if next 3 are numbers
                        float(parts[1])
                        float(parts[2])
                        float(parts[3])
                        return True
                except ValueError:
                    continue
        return False
    
    def _parse_smiles(self, smiles: str) -> Atoms:
        """Convert SMILES string to ASE Atoms object."""
        if not RDKIT_AVAILABLE:
            raise ImportError("RDKit is required for SMILES parsing")
        
        try:
            # Parse SMILES
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                raise ValueError(f"Invalid SMILES string: {smiles}")
            
            # Add hydrogens
            mol = Chem.AddHs(mol)
            
            # Generate 3D coordinates
            AllChem.EmbedMolecule(mol, randomSeed=42)
            AllChem.MMFFOptimizeMolecule(mol)
            
            # Extract coordinates and elements
            conf = mol.GetConformer()
            positions = []
            symbols = []
            
            for atom in mol.GetAtoms():
                pos = conf.GetAtomPosition(atom.GetIdx())
                positions.append([pos.x, pos.y, pos.z])
                symbols.append(atom.GetSymbol())
            
            # Create ASE Atoms object
            atoms = Atoms(symbols=symbols, positions=positions)
            
            # Add molecular properties as info
            atoms.info['smiles'] = smiles
            atoms.info['molecular_weight'] = rdMolDescriptors.CalcExactMolWt(mol)
            atoms.info['formula'] = rdMolDescriptors.CalcMolFormula(mol)
            
            return atoms
            
        except Exception as e:
            raise ValueError(f"Failed to parse SMILES '{smiles}': {str(e)}")
    
    def _parse_file(self, file_path: str, format_hint: Optional[str] = None) -> Atoms:
        """Parse molecular structure from file."""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # Determine format
        if format_hint:
            file_format = format_hint.lower()
        else:
            file_format = file_path.suffix.lower()
        
        try:
            if file_format in ['.xyz']:
                atoms = read(str(file_path), format='xyz')
            elif file_format in ['.sdf', '.mol']:
                atoms = self._parse_sdf(file_path)
            elif file_format in ['.pdb']:
                atoms = read(str(file_path), format='pdb')
            elif file_format in ['.gjf', '.com']:
                atoms = self._parse_gaussian_input(file_path)
            else:
                # Try ASE's automatic format detection
                atoms = read(str(file_path))
            
            # Add file info
            atoms.info['source_file'] = str(file_path)
            atoms.info['file_format'] = file_format
            
            return atoms
            
        except Exception as e:
            raise ValueError(f"Failed to parse file '{file_path}': {str(e)}")
    
    def _parse_sdf(self, file_path: Path) -> Atoms:
        """Parse SDF file using RDKit if available, otherwise use ASE."""
        if RDKIT_AVAILABLE:
            try:
                mol = Chem.MolFromMolFile(str(file_path))
                if mol is None:
                    raise ValueError("Invalid SDF file")
                
                # Extract coordinates and elements
                conf = mol.GetConformer()
                positions = []
                symbols = []
                
                for atom in mol.GetAtoms():
                    pos = conf.GetAtomPosition(atom.GetIdx())
                    positions.append([pos.x, pos.y, pos.z])
                    symbols.append(atom.GetSymbol())
                
                atoms = Atoms(symbols=symbols, positions=positions)
                
                # Add molecular properties
                atoms.info['molecular_weight'] = rdMolDescriptors.CalcExactMolWt(mol)
                atoms.info['formula'] = rdMolDescriptors.CalcMolFormula(mol)
                
                return atoms
                
            except Exception:
                pass
        
        # Fallback to ASE
        return read(str(file_path))
    
    def _parse_gaussian_input(self, file_path: Path) -> Atoms:
        """Parse Gaussian input file (.gjf, .com)."""
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        # Find coordinate section
        coord_start = None
        for i, line in enumerate(lines):
            if line.strip() and not line.startswith('#') and not line.startswith('%'):
                # Skip title and charge/multiplicity lines
                if coord_start is None:
                    coord_start = i + 2  # Skip title and charge/mult
                    break
        
        if coord_start is None:
            raise ValueError("No coordinates found in Gaussian input file")
        
        # Parse coordinates
        symbols = []
        positions = []
        
        for line in lines[coord_start:]:
            line = line.strip()
            if not line or line.startswith('--'):
                break
            
            parts = line.split()
            if len(parts) >= 4:
                symbol = parts[0]
                try:
                    x, y, z = float(parts[1]), float(parts[2]), float(parts[3])
                    symbols.append(symbol)
                    positions.append([x, y, z])
                except ValueError:
                    break
        
        if not symbols:
            raise ValueError("No valid coordinates found")
        
        return Atoms(symbols=symbols, positions=positions)
    
    def _parse_coordinate_string(self, coord_str: str) -> Atoms:
        """Parse coordinate string in XYZ format."""
        lines = coord_str.strip().split('\n')
        
        symbols = []
        positions = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            parts = line.split()
            if len(parts) >= 4:
                try:
                    symbol = parts[0]
                    x, y, z = float(parts[1]), float(parts[2]), float(parts[3])
                    
                    if symbol in chemical_symbols:
                        symbols.append(symbol)
                        positions.append([x, y, z])
                except ValueError:
                    continue
        
        if not symbols:
            raise ValueError("No valid coordinates found in string")
        
        return Atoms(symbols=symbols, positions=positions)
    
    def _parse_coordinate_list(self, coord_list: List) -> Atoms:
        """Parse coordinate list: [(symbol, x, y, z), ...]"""
        symbols = []
        positions = []
        
        for item in coord_list:
            if len(item) >= 4:
                symbol, x, y, z = item[0], float(item[1]), float(item[2]), float(item[3])
                symbols.append(symbol)
                positions.append([x, y, z])
            else:
                raise ValueError(f"Invalid coordinate entry: {item}")
        
        return Atoms(symbols=symbols, positions=positions)
    
    def validate_molecule(self, atoms: Atoms) -> Dict[str, Any]:
        """
        Validate molecular structure and return diagnostic information.
        
        Args:
            atoms: ASE Atoms object
            
        Returns:
            Dictionary with validation results
        """
        validation = {
            'is_valid': True,
            'warnings': [],
            'errors': [],
            'properties': {}
        }
        
        # Basic checks
        if len(atoms) == 0:
            validation['is_valid'] = False
            validation['errors'].append("Empty molecule")
            return validation
        
        # Check for reasonable bond distances
        distances = atoms.get_all_distances()
        min_distance = np.min(distances[distances > 0])
        
        if min_distance < 0.5:  # Å
            validation['warnings'].append(f"Very short bond distance: {min_distance:.3f} Å")
        
        # Check for isolated atoms (no bonds within 3 Å)
        max_reasonable_distance = 3.0
        isolated_atoms = []
        
        for i in range(len(atoms)):
            neighbors = np.sum(distances[i] < max_reasonable_distance) - 1  # Exclude self
            if neighbors == 0:
                isolated_atoms.append(i)
        
        if isolated_atoms:
            validation['warnings'].append(f"Isolated atoms found: {isolated_atoms}")
        
        # Calculate molecular properties
        validation['properties'] = {
            'n_atoms': len(atoms),
            'formula': atoms.get_chemical_formula(),
            'center_of_mass': atoms.get_center_of_mass().tolist(),
            'min_bond_distance': min_distance,
            'max_distance': np.max(distances)
        }
        
        return validation
    
    def save_molecule(self, atoms: Atoms, output_path: str, 
                     format: str = 'xyz') -> None:
        """
        Save ASE Atoms object to file.
        
        Args:
            atoms: ASE Atoms object
            output_path: Output file path
            format: Output format ('xyz', 'sdf', 'pdb', etc.)
        """
        try:
            write(output_path, atoms, format=format)
        except Exception as e:
            raise ValueError(f"Failed to save molecule: {str(e)}")


# Utility functions
def smiles_to_atoms(smiles: str) -> Atoms:
    """Quick utility to convert SMILES to ASE Atoms."""
    handler = InputHandler()
    return handler.parse_molecule(smiles)


def file_to_atoms(file_path: str) -> Atoms:
    """Quick utility to load molecule from file."""
    handler = InputHandler()
    return handler.parse_molecule(file_path)


if __name__ == "__main__":
    # Example usage
    handler = InputHandler()
    
    # Test SMILES parsing
    if RDKIT_AVAILABLE:
        try:
            ethanol = handler.parse_molecule("CCO")
            print(f"Ethanol: {ethanol.get_chemical_formula()}")
            print(f"Validation: {handler.validate_molecule(ethanol)}")
        except Exception as e:
            print(f"SMILES test failed: {e}")
    
    # Test coordinate string
    try:
        h2o_coords = """O 0.0 0.0 0.0
H 0.757 0.586 0.0
H -0.757 0.586 0.0"""
        
        water = handler.parse_molecule(h2o_coords)
        print(f"Water: {water.get_chemical_formula()}")
        print(f"Validation: {handler.validate_molecule(water)}")
    except Exception as e:
        print(f"Coordinate test failed: {e}")
