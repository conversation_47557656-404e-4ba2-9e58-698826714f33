Collecting pyscf
  Using cached pyscf-2.9.0-py3-none-macosx_11_0_arm64.whl.metadata (6.4 kB)
Requirement already satisfied: numpy!=1.16,!=1.17,>=1.13 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from pyscf) (2.3.1)
Requirement already satisfied: scipy>=1.6.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from pyscf) (1.16.0)
Collecting h5py>=2.7 (from pyscf)
  Using cached h5py-3.14.0-cp313-cp313-macosx_11_0_arm64.whl.metadata (2.7 kB)
Requirement already satisfied: setuptools in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from pyscf) (78.1.0)
Using cached pyscf-2.9.0-py3-none-macosx_11_0_arm64.whl (34.8 MB)
Using cached h5py-3.14.0-cp313-cp313-macosx_11_0_arm64.whl (2.8 MB)
Installing collected packages: h5py, pyscf
Successfully installed h5py-3.14.0 pyscf-2.9.0
