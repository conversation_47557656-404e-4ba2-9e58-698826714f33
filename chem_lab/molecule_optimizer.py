"""
Molecule Optimizer Module

DFT-level geometry optimization using ASE and PySCF with multiple methods
and basis sets. Includes frequency calculations and thermochemistry.

Author: AI Chemistry Lab
License: MIT
"""

import numpy as np
import warnings
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass
from ase import Atoms
from ase.optimize import BFGS, LBFGS, GPMin
from ase.vibrations import Vibrations
from ase.thermochemistry import IdealGasThermo

# PySCF is required for real quantum chemistry calculations
try:
    import pyscf
    from pyscf import gto, scf, dft
    PYSCF_AVAILABLE = True
except ImportError:
    PYSCF_AVAILABLE = False
    pyscf = None
    raise ImportError("PySCF is required for quantum chemistry calculations. Please install with: pip install pyscf")

try:
    from ase.calculators.emt import EMT
    EMT_AVAILABLE = True
except ImportError:
    EMT_AVAILABLE = False

# Create a custom PySCF calculator for ASE
from ase.calculators.calculator import Calculator, all_changes

class PySCF(Calculator):
    """
    Custom ASE calculator using PySCF for quantum chemistry calculations.
    """
    implemented_properties = ['energy', 'forces']

    def __init__(self, atom=None, basis='sto3g', xc='pbe', charge=0, spin=0, **kwargs):
        Calculator.__init__(self, **kwargs)
        self.atom = atom
        self.basis = basis
        self.xc = xc
        self.charge = charge
        self.spin = spin
        self.results = {}

    def calculate(self, atoms=None, properties=None, system_changes=all_changes):
        if atoms is not None:
            self.atom = atoms

        Calculator.calculate(self, atoms, properties, system_changes)

        # Convert ASE atoms to PySCF format
        atom_str = []
        for atom, pos in zip(self.atom.get_chemical_symbols(), self.atom.get_positions()):
            atom_str.append(f"{atom} {pos[0]} {pos[1]} {pos[2]}")

        # Create PySCF molecule
        mol = gto.Mole()
        mol.atom = ';'.join(atom_str)
        mol.basis = self.basis
        mol.charge = self.charge
        mol.spin = self.spin
        mol.build()

        # Run calculation
        if self.xc.lower() == 'hf':
            # Hartree-Fock calculation
            mf = scf.RHF(mol)
        else:
            # DFT calculation
            mf = dft.RKS(mol)
            mf.xc = self.xc

        # Run SCF
        energy = mf.kernel()

        # Convert to eV
        energy_ev = energy * 27.211396132  # Hartree to eV

        # Calculate forces (gradient)
        grad = mf.nuc_grad_method()
        forces = -grad.kernel() * 27.211396132 / 0.529177249  # Convert to eV/Å

        self.results = {
            'energy': energy_ev,
            'forces': forces
        }

try:
    from ase.calculators.gpaw import GPAW
    GPAW_AVAILABLE = True
except ImportError:
    GPAW_AVAILABLE = False


@dataclass
class OptimizationResult:
    """Container for optimization results."""
    atoms: Atoms
    energy: float
    forces: np.ndarray
    converged: bool
    n_steps: int
    method: str
    basis_set: str
    frequencies: Optional[np.ndarray] = None
    thermochemistry: Optional[Dict[str, float]] = None
    optimization_time: Optional[float] = None


class MoleculeOptimizer:
    """
    Handles DFT-level geometry optimization and property calculations.
    
    Supports multiple quantum chemistry methods:
    - PySCF (HF, DFT)
    - GPAW (real-space DFT)
    - EMT (for testing)
    """
    
    def __init__(self, method: str = "PBE", basis: str = "6-31G*",
                 charge: int = 0, spin: int = 0):
        """
        Initialize the molecule optimizer.
        
        Args:
            method: DFT method (PBE, B3LYP, M06-2X, etc.)
            basis: Basis set (STO-3G, 6-31G*, def2-TZVP, etc.)
            charge: Molecular charge
            spin: Spin multiplicity - 1
        """
        self.method = method
        self.basis = basis
        self.charge = charge
        self.spin = spin
        
        # Optimization parameters
        self.max_steps = 200
        self.force_tolerance = 0.05  # eV/Å
        self.energy_tolerance = 1e-6  # eV
        
        # Available methods and basis sets
        self.dft_methods = {
            'PBE': 'pbe',
            'B3LYP': 'b3lyp',
            'M06-2X': 'm062x',
            'PBE0': 'pbe0',
            'TPSS': 'tpss',
            'HF': 'hf'
        }
        
        self.basis_sets = {
            'STO-3G': 'sto3g',
            '6-31G': '631g',
            '6-31G*': '631gs',
            '6-31G**': '631gss',
            '6-311G*': '6311gs',
            'def2-SVP': 'def2svp',
            'def2-TZVP': 'def2tzvp',
            'cc-pVDZ': 'ccpvdz',
            'cc-pVTZ': 'ccpvtz'
        }
    
    def optimize_geometry(self, atoms: Atoms, 
                         calculator: Optional[str] = None,
                         optimizer: str = "BFGS") -> OptimizationResult:
        """
        Optimize molecular geometry.
        
        Args:
            atoms: ASE Atoms object
            calculator: Calculator to use ('pyscf', 'gpaw', 'emt')
            optimizer: Optimization algorithm ('BFGS', 'LBFGS', 'GPMin')
            
        Returns:
            OptimizationResult object
        """
        import time
        start_time = time.time()
        
        # Set up calculator
        calc = self._setup_calculator(atoms, calculator)
        atoms.set_calculator(calc)
        
        # Set up optimizer
        if optimizer == "BFGS":
            opt = BFGS(atoms, maxstep=0.2)
        elif optimizer == "LBFGS":
            opt = LBFGS(atoms, maxstep=0.2)
        elif optimizer == "GPMin":
            opt = GPMin(atoms)
        else:
            raise ValueError(f"Unknown optimizer: {optimizer}")
        
        # Run optimization
        try:
            opt.run(fmax=self.force_tolerance, steps=self.max_steps)
            converged = opt.converged()
            n_steps = opt.get_number_of_steps()
        except Exception as e:
            warnings.warn(f"Optimization failed: {e}")
            converged = False
            n_steps = self.max_steps
        
        # Get final energy and forces
        try:
            energy = atoms.get_potential_energy()
            forces = atoms.get_forces()
        except Exception as e:
            warnings.warn(f"Could not get energy/forces: {e}")
            energy = float('inf')
            forces = np.zeros((len(atoms), 3))
        
        optimization_time = time.time() - start_time
        
        return OptimizationResult(
            atoms=atoms.copy(),
            energy=energy,
            forces=forces,
            converged=converged,
            n_steps=n_steps,
            method=self.method,
            basis_set=self.basis,
            optimization_time=optimization_time
        )
    
    def calculate_frequencies(self, atoms: Atoms,
                            calculator: Optional[str] = None) -> Tuple[np.ndarray, Dict[str, float]]:
        """
        Calculate vibrational frequencies and thermochemistry.
        
        Args:
            atoms: Optimized ASE Atoms object
            calculator: Calculator to use
            
        Returns:
            Tuple of (frequencies, thermochemistry_dict)
        """
        
        # Set up calculator
        calc = self._setup_calculator(atoms, calculator)
        atoms.set_calculator(calc)
        
        try:
            # Calculate vibrations
            vib = Vibrations(atoms)
            vib.run()
            
            # Get frequencies (in cm⁻¹)
            frequencies = vib.get_frequencies()
            
            # Calculate thermochemistry at 298.15 K
            thermo = IdealGasThermo(
                vib_energies=frequencies * 1e-3 * 8.065544e-5,  # Convert to eV
                potentialenergy=atoms.get_potential_energy(),
                atoms=atoms,
                geometry='nonlinear' if len(atoms) > 2 else 'linear',
                symmetrynumber=1,  # Simplified
                spin=self.spin
            )
            
            # Get thermodynamic properties at 298.15 K, 1 atm
            temperature = 298.15
            pressure = 101325  # Pa
            
            thermochemistry = {
                'enthalpy': thermo.get_enthalpy(temperature),
                'entropy': thermo.get_entropy(temperature, pressure),
                'gibbs_energy': thermo.get_gibbs_energy(temperature, pressure),
                'heat_capacity': thermo.get_heat_capacity(temperature),
                'zero_point_energy': thermo.get_ZPE_correction()
            }
            
            # Clean up vibration files
            vib.clean()
            
            return frequencies, thermochemistry
            
        except Exception as e:
            warnings.warn(f"Frequency calculation failed: {e}")
            return np.array([]), {}
    
    def single_point_energy(self, atoms: Atoms,
                           calculator: Optional[str] = None) -> float:
        """
        Calculate single-point energy.
        
        Args:
            atoms: ASE Atoms object
            calculator: Calculator to use
            
        Returns:
            Energy in eV
        """
        calc = self._setup_calculator(atoms, calculator)
        atoms.set_calculator(calc)
        
        try:
            return atoms.get_potential_energy()
        except Exception as e:
            warnings.warn(f"Single point calculation failed: {e}")
            return float('inf')
    
    def _setup_calculator(self, atoms: Atoms,
                         calculator: Optional[str] = None) -> Any:
        """Set up quantum chemistry calculator."""

        if calculator is None:
            # Auto-select best available calculator
            if PYSCF_AVAILABLE:
                calculator = 'pyscf'
            elif GPAW_AVAILABLE:
                calculator = 'gpaw'
            elif EMT_AVAILABLE:
                calculator = 'emt'
            else:
                raise RuntimeError("No quantum chemistry calculator available. Please install PySCF with: pip install pyscf")

        if calculator.lower() == 'pyscf' and PYSCF_AVAILABLE:
            return self._setup_pyscf(atoms)
        elif calculator.lower() == 'gpaw' and GPAW_AVAILABLE:
            return self._setup_gpaw(atoms)
        elif calculator.lower() == 'emt' and EMT_AVAILABLE:
            return EMT()
        else:
            raise ValueError(f"Calculator '{calculator}' not available. Please install the required package.")
    
    def _setup_pyscf(self, atoms: Atoms):
        """Set up PySCF calculator."""

        if not PYSCF_AVAILABLE or pyscf is None:
            raise ImportError("PySCF not available")

        # Map method and basis to PySCF format
        method = self.dft_methods.get(self.method.upper(), self.method.lower())
        basis = self.basis_sets.get(self.basis, self.basis.lower())

        # Determine if it's a DFT or HF calculation
        if method == 'hf':
            calc_type = 'hf'
        else:
            calc_type = 'dft'

        try:
            calc = PySCF(
                atom=atoms,
                basis=basis,
                xc=method,
                charge=self.charge,
                spin=self.spin
            )
            return calc
        except Exception as e:
            warnings.warn(f"PySCF setup failed: {e}")
            # Fallback to simpler settings
            return PySCF(
                atom=atoms,
                basis='sto3g',
                xc='pbe',
                charge=self.charge,
                spin=self.spin
            )
    
    def _setup_gpaw(self, atoms: Atoms):
        """Set up GPAW calculator."""

        if not GPAW_AVAILABLE or GPAW is None:
            raise ImportError("GPAW not available")

        # Map method to GPAW format
        xc = self.method.upper()
        if xc not in ['PBE', 'LDA', 'RPBE', 'revPBE']:
            xc = 'PBE'  # Default

        try:
            calc = GPAW(
                mode='lcao',
                xc=xc,
                basis='dzp',
                charge=self.charge,
                spinpol=(self.spin != 0),
                convergence={'energy': 1e-6, 'density': 1e-4},
                maxiter=300,
                txt=None  # Suppress output
            )
            return calc
        except Exception as e:
            warnings.warn(f"GPAW setup failed: {e}")
            # Fallback to basic settings
            return GPAW(mode='lcao', xc='PBE', txt=None)
    
    def optimize_with_constraints(self, atoms: Atoms,
                                constraints: List[Any],
                                calculator: Optional[str] = None) -> OptimizationResult:
        """
        Optimize geometry with constraints.
        
        Args:
            atoms: ASE Atoms object
            constraints: List of ASE constraint objects
            calculator: Calculator to use
            
        Returns:
            OptimizationResult object
        """
        
        # Apply constraints
        atoms.set_constraint(constraints)
        
        # Run optimization
        return self.optimize_geometry(atoms, calculator)
    
    def scan_coordinate(self, atoms: Atoms, 
                       coordinate_func: callable,
                       scan_range: Tuple[float, float],
                       n_points: int = 10,
                       calculator: Optional[str] = None) -> Dict[str, np.ndarray]:
        """
        Perform a coordinate scan (e.g., bond length, angle).
        
        Args:
            atoms: ASE Atoms object
            coordinate_func: Function that modifies the coordinate
            scan_range: (min_value, max_value) for scan
            n_points: Number of scan points
            calculator: Calculator to use
            
        Returns:
            Dictionary with 'coordinates' and 'energies' arrays
        """
        
        calc = self._setup_calculator(atoms, calculator)
        
        coordinates = np.linspace(scan_range[0], scan_range[1], n_points)
        energies = []
        
        for coord_value in coordinates:
            # Modify coordinate
            atoms_copy = atoms.copy()
            coordinate_func(atoms_copy, coord_value)
            
            # Calculate energy
            atoms_copy.set_calculator(calc)
            try:
                energy = atoms_copy.get_potential_energy()
            except:
                energy = float('inf')
            
            energies.append(energy)
        
        return {
            'coordinates': coordinates,
            'energies': np.array(energies)
        }
    
    def get_molecular_orbitals(self, atoms: Atoms,
                              calculator: Optional[str] = None) -> Dict[str, Any]:
        """
        Get molecular orbital information (PySCF only).
        
        Args:
            atoms: ASE Atoms object
            calculator: Calculator to use
            
        Returns:
            Dictionary with MO energies and coefficients
        """
        
        if not PYSCF_AVAILABLE:
            raise ImportError("PySCF required for molecular orbital analysis")
        
        calc = self._setup_pyscf(atoms)
        atoms.set_calculator(calc)
        
        # Get energy to ensure calculation is done
        atoms.get_potential_energy()
        
        # Extract MO information from PySCF
        try:
            mo_energies = calc.results.get('mo_energy', [])
            mo_coeffs = calc.results.get('mo_coeff', [])
            
            return {
                'mo_energies': mo_energies,
                'mo_coefficients': mo_coeffs,
                'homo_energy': mo_energies[calc.results.get('nelec', 0)//2 - 1] if mo_energies else None,
                'lumo_energy': mo_energies[calc.results.get('nelec', 0)//2] if len(mo_energies) > calc.results.get('nelec', 0)//2 else None
            }
        except Exception as e:
            warnings.warn(f"Could not extract MO information: {e}")
            return {}


    def estimate_computational_cost(self, atoms: Atoms,
                                  method: str, basis: str) -> Dict[str, Any]:
        """
        Estimate computational cost for given method/basis.

        Args:
            atoms: ASE Atoms object
            method: DFT method
            basis: Basis set

        Returns:
            Dictionary with cost estimates
        """

        n_atoms = len(atoms)
        n_electrons = sum(atoms.get_atomic_numbers())

        # Rough cost estimates (relative units)
        method_costs = {
            'HF': 1.0,
            'PBE': 1.2,
            'B3LYP': 2.0,
            'M06-2X': 3.0,
            'PBE0': 2.5
        }

        basis_costs = {
            'STO-3G': 1.0,
            '6-31G': 2.0,
            '6-31G*': 3.0,
            '6-31G**': 4.0,
            'def2-TZVP': 8.0,
            'cc-pVTZ': 15.0
        }

        base_cost = n_atoms ** 2.5 * n_electrons ** 1.5
        method_factor = method_costs.get(method.upper(), 2.0)
        basis_factor = basis_costs.get(basis, 3.0)

        total_cost = base_cost * method_factor * basis_factor

        # Estimate time categories
        if total_cost < 1000:
            time_estimate = "< 1 minute"
            difficulty = "easy"
        elif total_cost < 10000:
            time_estimate = "1-10 minutes"
            difficulty = "moderate"
        elif total_cost < 100000:
            time_estimate = "10-60 minutes"
            difficulty = "hard"
        else:
            time_estimate = "> 1 hour"
            difficulty = "very_hard"

        return {
            'relative_cost': total_cost,
            'time_estimate': time_estimate,
            'difficulty': difficulty,
            'n_atoms': n_atoms,
            'n_electrons': n_electrons,
            'method_factor': method_factor,
            'basis_factor': basis_factor
        }


if __name__ == "__main__":
    # Example usage
    from input_handler import InputHandler

    # Create a simple molecule
    handler = InputHandler()

    try:
        # Test with water molecule
        water_coords = """O 0.0 0.0 0.0
H 0.757 0.586 0.0
H -0.757 0.586 0.0"""

        atoms = handler.parse_molecule(water_coords)

        # Initialize optimizer
        optimizer = MoleculeOptimizer(method="PBE", basis="STO-3G")

        # Estimate cost
        cost = optimizer.estimate_computational_cost(atoms, "PBE", "STO-3G")
        print(f"Computational cost estimate: {cost}")

        # Optimize geometry
        result = optimizer.optimize_geometry(atoms)

        print(f"Optimization converged: {result.converged}")
        print(f"Final energy: {result.energy:.6f} eV")
        print(f"Number of steps: {result.n_steps}")
        print(f"Method: {result.method}/{result.basis_set}")

        # Calculate frequencies if optimization converged
        if result.converged:
            try:
                frequencies, thermo = optimizer.calculate_frequencies(result.atoms)
                print(f"Frequencies calculated: {len(frequencies)} modes")
                if thermo:
                    print(f"Gibbs energy: {thermo.get('gibbs_energy', 'N/A'):.6f} eV")
            except Exception as e:
                print(f"Frequency calculation failed: {e}")

    except Exception as e:
        print(f"Test failed: {e}")
