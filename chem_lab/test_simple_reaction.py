#!/usr/bin/env python3
"""
Test H2 + O2 reaction with simplified energy calculations
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from product_predictor import ProductPredictor
from reaction_feasibility import ReactionFeas<PERSON><PERSON>hecker
from input_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_with_known_energies():
    """Test feasibility with known literature energies"""
    print("🧪 Testing H2 + O2 with Known Literature Energies")
    print("=" * 60)
    
    checker = ReactionFeasibilityChecker()
    
    # Known literature values (in eV):
    # H2: 0 eV (reference)
    # O2: 0 eV (reference) 
    # H2O: -5.1 eV (formation energy from elements)
    
    # For reaction: H2 + 1/2 O2 -> H2O
    # ΔH = -5.1 eV = -492 kJ/mol (literature: ~-480 kJ/mol)
    
    h2_energy = 0.0  # Reference
    o2_energy = 0.0  # Reference
    h2o_energy = -5.1  # Formation energy
    
    print(f"Using literature energies:")
    print(f"  H2: {h2_energy:.1f} eV")
    print(f"  O2: {o2_energy:.1f} eV") 
    print(f"  H2O: {h2o_energy:.1f} eV")
    
    # Calculate thermodynamics
    thermo = checker.calculate_thermodynamics_from_energies(
        reactant_energy=h2_energy + 0.5 * o2_energy,  # H2 + 1/2 O2
        product_energy=h2o_energy,  # H2O
        temperature=600.0
    )
    
    print(f"\nThermodynamic analysis:")
    print(f"  ΔE: {thermo['delta_e']:.3f} eV")
    print(f"  ΔH: {thermo['delta_h']:.3f} eV")
    print(f"  ΔG: {thermo['delta_g']:.3f} eV")
    print(f"  K: {thermo['equilibrium_constant']:.2e}")
    print(f"  Spontaneous: {thermo['spontaneous']}")
    
    # Check feasibility criteria
    print(f"\nFeasibility criteria:")
    print(f"  ΔG < 2.0 eV? {thermo['delta_g'] < 2.0}")
    print(f"  K > 1e-12? {thermo['equilibrium_constant'] > 1e-12}")
    print(f"  Should be feasible: {thermo['delta_g'] < 2.0 or thermo['equilibrium_constant'] > 1e-12}")

def test_with_corrected_stoichiometry():
    """Test with correct stoichiometry: 2H2 + O2 -> 2H2O"""
    print("\n\n🧪 Testing 2H2 + O2 -> 2H2O with Literature Energies")
    print("=" * 60)
    
    checker = ReactionFeasibilityChecker()
    
    # For reaction: 2H2 + O2 -> 2H2O
    # ΔH = 2 × (-5.1) = -10.2 eV = -984 kJ/mol
    
    reactant_energy = 2 * 0.0 + 1 * 0.0  # 2H2 + O2
    product_energy = 2 * (-5.1)  # 2H2O
    
    print(f"Reaction: 2H2 + O2 -> 2H2O")
    print(f"  Reactants: 2H2 + O2 = {reactant_energy:.1f} eV")
    print(f"  Products: 2H2O = {product_energy:.1f} eV")
    
    thermo = checker.calculate_thermodynamics_from_energies(
        reactant_energy=reactant_energy,
        product_energy=product_energy,
        temperature=600.0
    )
    
    print(f"\nThermodynamic analysis:")
    print(f"  ΔE: {thermo['delta_e']:.3f} eV")
    print(f"  ΔH: {thermo['delta_h']:.3f} eV") 
    print(f"  ΔG: {thermo['delta_g']:.3f} eV")
    print(f"  K: {thermo['equilibrium_constant']:.2e}")
    print(f"  Spontaneous: {thermo['spontaneous']}")
    
    print(f"\nFeasibility criteria:")
    print(f"  ΔG < 2.0 eV? {thermo['delta_g'] < 2.0}")
    print(f"  K > 1e-12? {thermo['equilibrium_constant'] > 1e-12}")
    print(f"  Should be feasible: {thermo['delta_g'] < 2.0 or thermo['equilibrium_constant'] > 1e-12}")

def test_full_reaction_with_emt():
    """Test the full reaction using EMT calculator (faster, more stable)"""
    print("\n\n🧪 Testing Full Reaction with EMT Calculator")
    print("=" * 60)
    
    # This would require modifying the optimizer to use EMT
    # EMT is much faster and more stable than PySCF for simple molecules
    print("Note: This would require switching to EMT calculator")
    print("EMT gives reasonable energies for simple molecules like H2, O2, H2O")
    print("and converges much more reliably than PySCF for these systems.")

if __name__ == "__main__":
    test_with_known_energies()
    test_with_corrected_stoichiometry()
    test_full_reaction_with_emt()
