#!/usr/bin/env python3
"""
Test script for the enhanced validation system.

This script tests the new validation rules and fallback logic
without running a full DFT calculation.

Author: AI Chemistry Lab
License: MIT
"""

import sys
import json
from pathlib import Path
from typing import Dict, Any

# Mock classes to simulate results for testing
class MockPathwayResult:
    def __init__(self, converged=True, activation_energy=2.0, reaction_energy=0.5):
        self.converged = converged
        self.activation_energy = activation_energy  # eV
        self.reaction_energy = reaction_energy      # eV

class MockFeasibilityResult:
    def __init__(self):
        self.is_feasible = True
        self.confidence = 0.85
        self.activation_barrier_estimate = 1.2  # eV
        self.delta_g = -0.048  # eV (-1.1 kcal/mol)
        self.delta_h = -0.061  # eV (-1.4 kcal/mol)
        self.computation_method = "ML_ensemble"
        self.ml_predictions = {
            "bayesian_reactivity": 0.82,
            "molecular_transformer": 0.88,
            "coeffnet": 0.83
        }

class MockPredictionResult:
    def __init__(self):
        self.products = ["CC(=O)OCC", "O"]  # Ethyl acetate + water
        self.confidence = 0.85

class MockReactionNetwork:
    def __init__(self):
        self.edges = [MockEdge()]
        self.nodes = {
            "reactants": MockNode("CCO.CC(=O)O"),
            "products": MockNode("CC(=O)OCC.O")
        }
        self.properties = {"n_pathways": 1}

class MockEdge:
    def __init__(self):
        self.activation_energy = 0.5  # kcal/mol

class MockNode:
    def __init__(self, smiles):
        self.smiles = smiles

def test_validation_scenarios():
    """Test different validation scenarios."""
    
    print("🧪 Testing Enhanced Validation System")
    print("=" * 50)
    
    # Import our validation system
    try:
        from result_validator import ResultValidator, ValidationStatus, PredictionSource
        from enhanced_result_saver import EnhancedResultSaver
        validator = ResultValidator()
        print("✅ Validation modules imported successfully")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return
    
    # Test Scenario 1: All methods work and agree (ideal case)
    print("\n📋 Test 1: High-confidence consensus (ML + Network agree)")
    
    pathway_result = MockPathwayResult(converged=True, activation_energy=1.0)
    feasibility_result = MockFeasibilityResult()
    product_result = MockPredictionResult()
    network_result = MockReactionNetwork()
    thermo_data = {"rate_constant": 1.5e-3, "delta_g": -1.1}
    
    validated_prediction = validator.validate_and_select_prediction(
        pathway_result=pathway_result,
        feasibility_result=feasibility_result,
        product_result=product_result,
        network_result=network_result,
        thermo_data=thermo_data
    )
    
    print(f"   Source: {validated_prediction.prediction_source.value}")
    print(f"   Confidence: {validated_prediction.confidence_score:.2f}")
    print(f"   Status: {validated_prediction.validation_status.value}")
    print(f"   Products: {validated_prediction.products}")
    print(f"   Activation Energy: {validated_prediction.activation_energy:.1f} kcal/mol")
    
    # Test Scenario 2: DFT fails to converge
    print("\n📋 Test 2: DFT convergence failure (fallback to ML)")
    
    failed_pathway = MockPathwayResult(converged=False, activation_energy=500.0)
    
    validated_prediction_2 = validator.validate_and_select_prediction(
        pathway_result=failed_pathway,
        feasibility_result=feasibility_result,
        product_result=product_result,
        network_result=network_result,
        thermo_data={"rate_constant": 0.0}
    )
    
    print(f"   Source: {validated_prediction_2.prediction_source.value}")
    print(f"   Confidence: {validated_prediction_2.confidence_score:.2f}")
    print(f"   Status: {validated_prediction_2.validation_status.value}")
    print(f"   Validation messages: {len(validated_prediction_2.validation_messages)} messages")
    
    # Test Scenario 3: Unrealistic activation energy
    print("\n📋 Test 3: Unrealistic activation energy (> 200 kcal/mol)")
    
    high_energy_pathway = MockPathwayResult(converged=True, activation_energy=10.0)  # ~230 kcal/mol
    
    validated_prediction_3 = validator.validate_and_select_prediction(
        pathway_result=high_energy_pathway,
        feasibility_result=feasibility_result,
        product_result=product_result,
        network_result=network_result,
        thermo_data=thermo_data
    )
    
    print(f"   Source: {validated_prediction_3.prediction_source.value}")
    print(f"   Confidence: {validated_prediction_3.confidence_score:.2f}")
    print(f"   Status: {validated_prediction_3.validation_status.value}")
    
    # Test Scenario 4: No DFT, ML + Network only
    print("\n📋 Test 4: No DFT available (ML + Network only)")
    
    validated_prediction_4 = validator.validate_and_select_prediction(
        pathway_result=None,
        feasibility_result=feasibility_result,
        product_result=product_result,
        network_result=network_result,
        thermo_data=None
    )
    
    print(f"   Source: {validated_prediction_4.prediction_source.value}")
    print(f"   Confidence: {validated_prediction_4.confidence_score:.2f}")
    print(f"   Status: {validated_prediction_4.validation_status.value}")
    
    # Test Enhanced Result Saver
    print("\n📋 Test 5: Enhanced Result Saver")
    
    try:
        test_output_dir = "test_validation_output"
        saver = EnhancedResultSaver(test_output_dir)
        
        raw_results = {
            'pathway_result': pathway_result,
            'feasibility_result': feasibility_result,
            'product_result': product_result,
            'network_result': network_result,
            'thermo_data': thermo_data,
            'ml_results': {'feasibility_result': feasibility_result.__dict__}
        }
        
        report_path = saver.save_comprehensive_results(
            validated_prediction=validated_prediction,
            raw_results=raw_results,
            reactants=["CCO", "CC(=O)O"],
            conditions={"temperature": 373.15, "solvent": "toluene"}
        )
        
        print(f"   ✅ Report saved to: {report_path}")
        
        # Check if directories were created
        base_path = Path(test_output_dir)
        expected_dirs = ['ML_outputs', 'Validated_outputs', 'raw_data']
        for dir_name in expected_dirs:
            if (base_path / dir_name).exists():
                print(f"   ✅ Directory created: {dir_name}")
            else:
                print(f"   ❌ Directory missing: {dir_name}")
        
        # Check if final report exists
        if (base_path / 'final_report.json').exists():
            print("   ✅ Final report created")
            
            # Load and display key info from report
            with open(base_path / 'final_report.json', 'r') as f:
                report = json.load(f)
            
            print(f"   📊 Report confidence: {report['final_prediction']['confidence_score']:.2f}")
            print(f"   🎯 Prediction source: {report['prediction_metadata']['source']}")
            print(f"   ✅ Products: {report['final_prediction']['products']}")
        
    except Exception as e:
        print(f"   ❌ Enhanced saver test failed: {e}")
    
    print("\n🎉 Validation system testing completed!")
    print("\n📝 Summary of Key Features Tested:")
    print("   ✅ Convergence validation (Rule 1)")
    print("   ✅ Activation energy thresholds (Rule 2)")
    print("   ✅ Model agreement assessment (Rule 3)")
    print("   ✅ Fallback logic implementation")
    print("   ✅ Enhanced result organization")
    print("   ✅ Comprehensive reporting")

if __name__ == "__main__":
    test_validation_scenarios()
