"""
Product Predictor Module

Predicts reaction products using LLM APIs (OpenAI, Perplexity) with RDKit fallback.
Implements intelligent reaction prediction with confidence scoring.

Author: AI Chemistry Lab
License: MIT
"""

import os
import re
import json
import time
import requests
import warnings
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    warnings.warn("OpenAI not available. LLM prediction disabled.")

# RDKit is required for proper cheminformatics
try:
    from rdkit import Chem
    from rdkit.Chem import AllChem, rdChemReactions
    RDKIT_AVAILABLE = True
except ImportError:
    RDKIT_AVAILABLE = False
    raise ImportError("RDKit is required for molecular handling and product prediction. Please install with: pip install rdkit-pypi")

# Import surrogate models
try:
    from surrogate_models import SurrogateModelManager
    SURROGATE_AVAILABLE = True
except ImportError:
    SURROGATE_AVAILABLE = False


@dataclass
class PredictionResult:
    """Container for reaction prediction results."""
    products: List[str]
    confidence: float
    method: str
    reasoning: Optional[str] = None
    alternative_products: Optional[List[str]] = None
    reaction_type: Optional[str] = None


class ProductPredictor:
    """
    Predicts reaction products using multiple approaches:
    1. LLM-based prediction (OpenAI, Perplexity)
    2. RDKit reaction templates
    3. Rule-based heuristics
    """
    
    def __init__(self, openai_api_key: Optional[str] = None,
                 perplexity_api_key: Optional[str] = None,
                 use_surrogate_models: bool = True):
        """
        Initialize the product predictor.

        Args:
            openai_api_key: OpenAI API key
            perplexity_api_key: Perplexity API key
            use_surrogate_models: Whether to use ML surrogate models
        """
        self.openai_api_key = openai_api_key
        self.perplexity_api_key = perplexity_api_key
        self.use_surrogate_models = use_surrogate_models and SURROGATE_AVAILABLE

        # Initialize surrogate model manager
        if self.use_surrogate_models:
            try:
                self.surrogate_manager = SurrogateModelManager()
                print("✅ Surrogate models initialized for product prediction")
            except Exception as e:
                warnings.warn(f"Failed to initialize surrogate models: {e}")
                self.use_surrogate_models = False

        # Initialize OpenAI client
        if OPENAI_AVAILABLE and openai_api_key:
            openai.api_key = openai_api_key
            self.openai_client = openai
        else:
            self.openai_client = None
        
        # Load reaction templates
        self.reaction_templates = self._load_reaction_templates()
        
        # Rate limiting
        self.last_api_call = 0
        self.min_api_interval = 1.0  # seconds
    
    def predict_products(self, reactants: List[str],
                        temperature: float = 298.15,
                        pressure: float = 1.0,
                        solvent: str = "vacuum",
                        catalyst: Optional[str] = None,
                        use_llm: bool = True) -> PredictionResult:
        """
        Predict reaction products from reactants using ML models with fallbacks.

        Args:
            reactants: List of reactant SMILES strings
            temperature: Reaction temperature (K)
            pressure: Reaction pressure (atm)
            solvent: Solvent name
            catalyst: Optional catalyst SMILES
            use_llm: Whether to use LLM prediction

        Returns:
            PredictionResult object
        """

        # Try ML surrogate models first
        if self.use_surrogate_models:
            try:
                result = self._predict_with_ml(
                    reactants, temperature, pressure, solvent, catalyst
                )
                if result.confidence > 0.6:  # ML confidence threshold
                    return result
            except Exception as e:
                warnings.warn(f"ML prediction failed: {e}")

        # Try LLM prediction
        if use_llm and (self.openai_client or self.perplexity_api_key):
            try:
                result = self._predict_with_llm(
                    reactants, temperature, pressure, solvent, catalyst
                )
                if result.confidence > 0.7:  # High confidence threshold
                    return result
            except Exception as e:
                warnings.warn(f"LLM prediction failed: {e}")

        # Fallback to RDKit templates
        if RDKIT_AVAILABLE:
            try:
                result = self._predict_with_rdkit(reactants, temperature)
                if result.products:
                    return result
            except Exception as e:
                warnings.warn(f"RDKit prediction failed: {e}")

        # Final fallback to heuristics
        return self._predict_with_heuristics(reactants, temperature)

    def _predict_with_ml(self, reactants: List[str], temperature: float,
                        pressure: float, solvent: str,
                        catalyst: Optional[str]) -> PredictionResult:
        """Predict products using ML surrogate models."""

        # Extract features for ML models
        features = self.surrogate_manager.extract_reaction_features(
            reactants=reactants,
            temperature=temperature,
            pressure=pressure,
            solvent=solvent,
            catalyst=catalyst
        )

        # Get ML product prediction
        ml_result = self.surrogate_manager.predict_products(features)

        # Convert to PredictionResult format
        return PredictionResult(
            products=ml_result.prediction,
            confidence=ml_result.confidence,
            method=f"ML-{ml_result.model_name}",
            reasoning=f"ML prediction using {ml_result.model_name}",
            reaction_type="ml_predicted"
        )

    def _predict_with_llm(self, reactants: List[str], temperature: float,
                         pressure: float, solvent: str, 
                         catalyst: Optional[str]) -> PredictionResult:
        """Predict products using LLM APIs."""
        
        # Try OpenAI first
        if self.openai_client:
            try:
                return self._predict_with_openai(
                    reactants, temperature, pressure, solvent, catalyst
                )
            except Exception as e:
                warnings.warn(f"OpenAI prediction failed: {e}")
        
        # Try Perplexity as backup
        if self.perplexity_api_key:
            try:
                return self._predict_with_perplexity(
                    reactants, temperature, pressure, solvent, catalyst
                )
            except Exception as e:
                warnings.warn(f"Perplexity prediction failed: {e}")
        
        raise ValueError("No LLM services available")
    
    def _predict_with_openai(self, reactants: List[str], temperature: float,
                           pressure: float, solvent: str,
                           catalyst: Optional[str]) -> PredictionResult:
        """Predict products using OpenAI API."""
        
        self._rate_limit()
        
        # Construct prompt
        prompt = self._build_llm_prompt(
            reactants, temperature, pressure, solvent, catalyst
        )
        
        try:
            response = self.openai_client.ChatCompletion.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are an expert organic chemist."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,  # Low temperature for consistency
                max_tokens=500
            )
            
            content = response.choices[0].message.content
            return self._parse_llm_response(content, "OpenAI")
            
        except Exception as e:
            raise ValueError(f"OpenAI API error: {e}")
    
    def _predict_with_perplexity(self, reactants: List[str], temperature: float,
                               pressure: float, solvent: str,
                               catalyst: Optional[str]) -> PredictionResult:
        """Predict products using Perplexity API."""
        
        self._rate_limit()
        
        prompt = self._build_llm_prompt(
            reactants, temperature, pressure, solvent, catalyst
        )
        
        headers = {
            "Authorization": f"Bearer {self.perplexity_api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "llama-3.1-sonar-large-128k-online",
            "messages": [
                {"role": "system", "content": "You are an expert organic chemist."},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.1,
            "max_tokens": 500
        }
        
        try:
            response = requests.post(
                "https://api.perplexity.ai/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            response.raise_for_status()
            
            content = response.json()["choices"][0]["message"]["content"]
            return self._parse_llm_response(content, "Perplexity")
            
        except Exception as e:
            raise ValueError(f"Perplexity API error: {e}")
    
    def _build_llm_prompt(self, reactants: List[str], temperature: float,
                         pressure: float, solvent: str,
                         catalyst: Optional[str]) -> str:
        """Build prompt for LLM prediction."""
        
        reactant_str = " + ".join(reactants)
        
        prompt = f"""
Predict the most likely products of this chemical reaction:

Reactants: {reactant_str}
Temperature: {temperature} K
Pressure: {pressure} atm
Solvent: {solvent}
"""
        
        if catalyst:
            prompt += f"Catalyst: {catalyst}\n"
        
        prompt += """
Please provide:
1. The most likely product(s) as SMILES strings
2. Confidence level (0-1)
3. Reaction type/mechanism
4. Brief reasoning

Format your response as JSON:
{
    "products": ["SMILES1", "SMILES2"],
    "confidence": 0.85,
    "reaction_type": "substitution",
    "reasoning": "explanation"
}
"""
        
        return prompt
    
    def _parse_llm_response(self, content: str, method: str) -> PredictionResult:
        """Parse LLM response and extract prediction data."""
        
        try:
            # Try to extract JSON from response
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group())
                
                return PredictionResult(
                    products=data.get("products", []),
                    confidence=data.get("confidence", 0.5),
                    method=method,
                    reasoning=data.get("reasoning"),
                    reaction_type=data.get("reaction_type")
                )
        except:
            pass
        
        # Fallback: extract SMILES patterns from text
        smiles_pattern = r'[A-Za-z0-9@+\-\[\]()=#$:.\\\/]+'
        potential_smiles = re.findall(smiles_pattern, content)
        
        # Filter for valid SMILES
        products = []
        if RDKIT_AVAILABLE:
            for smiles in potential_smiles:
                if len(smiles) > 2 and Chem.MolFromSmiles(smiles):
                    products.append(smiles)
        
        return PredictionResult(
            products=products[:3],  # Limit to top 3
            confidence=0.3,  # Low confidence for parsed response
            method=f"{method} (parsed)",
            reasoning="Extracted from text response"
        )
    
    def _predict_with_rdkit(self, reactants: List[str], 
                          temperature: float) -> PredictionResult:
        """Predict products using RDKit reaction templates."""
        
        if not RDKIT_AVAILABLE:
            raise ImportError("RDKit not available")
        
        # Convert reactants to molecules
        reactant_mols = []
        for smiles in reactants:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                raise ValueError(f"Invalid SMILES: {smiles}")
            reactant_mols.append(mol)
        
        # Try reaction templates
        best_products = []
        best_confidence = 0.0
        best_reaction_type = None
        
        for template_name, template_smarts in self.reaction_templates.items():
            try:
                reaction = rdChemReactions.ReactionFromSmarts(template_smarts)
                
                # Try to apply reaction
                products = reaction.RunReactants(reactant_mols)
                
                if products:
                    # Convert products to SMILES
                    product_smiles = []
                    for product_set in products[0]:  # Take first product set
                        smiles = Chem.MolToSmiles(product_set)
                        product_smiles.append(smiles)
                    
                    # Simple confidence based on template specificity
                    confidence = min(0.8, 0.5 + 0.1 * len(product_smiles))
                    
                    if confidence > best_confidence:
                        best_products = product_smiles
                        best_confidence = confidence
                        best_reaction_type = template_name
                        
            except Exception:
                continue
        
        return PredictionResult(
            products=best_products,
            confidence=best_confidence,
            method="RDKit",
            reaction_type=best_reaction_type
        )
    
    def _predict_with_heuristics(self, reactants: List[str],
                               temperature: float) -> PredictionResult:
        """Predict products using simple heuristics."""

        # Very basic heuristics for common reactions
        products = []
        reaction_type = "unknown"
        reasoning = "Basic heuristic prediction"
        confidence = 0.1  # Default low confidence

        # Count specific reactants
        h2_count = sum(1 for r in reactants if r in ['[H][H]', 'H'])
        o2_count = sum(1 for r in reactants if r in ['O=O', '[O][O]'])

        # Check for H2 + O2 -> H2O reaction (hydrogen combustion)
        if h2_count >= 1 and o2_count >= 1:
            # H2 + O2 -> H2O reaction
            # Stoichiometry: 2H2 + O2 -> 2H2O
            water_count = min(h2_count, 2 * o2_count)
            products = ['O'] * water_count  # Water as 'O' in SMILES
            reaction_type = "hydrogen_combustion"
            reasoning = f"Hydrogen combustion: {h2_count}H2 + {o2_count}O2 -> {water_count}H2O"
            confidence = 0.8  # High confidence for this well-known reaction

        elif len(reactants) == 2:
            # Check for specific reaction patterns
            reactant_set = set(reactants)

            # Esterification: Ethanol + Acetic acid -> Ethyl acetate + Water
            if 'CCO' in reactant_set and 'CC(=O)O' in reactant_set:
                products = ['CC(=O)OCC', 'O']  # Ethyl acetate + Water
                reaction_type = "esterification"
                reasoning = "Esterification reaction: acetic acid + ethanol -> ethyl acetate + water"
                confidence = 0.8

            # Methane + Oxygen -> CO2 + H2O (combustion)
            elif 'C' in reactant_set and 'O=O' in reactant_set:
                products = ['O=C=O', 'O', 'O']  # CO2 + 2H2O
                reaction_type = "combustion"
                reasoning = "Methane combustion: CH4 + 2O2 -> CO2 + 2H2O"
                confidence = 0.7

            # Ethylene + Hydrogen -> Ethane (hydrogenation)
            elif 'C=C' in reactant_set and '[H][H]' in reactant_set:
                products = ['CC']  # Ethane
                reaction_type = "hydrogenation"
                reasoning = "Alkene hydrogenation: C2H4 + H2 -> C2H6"
                confidence = 0.7

            # General combustion: hydrocarbon + oxygen
            elif any('O=O' in r for r in reactants) and any('C' in r for r in reactants):
                products = ['O=C=O', 'O']  # CO2 + H2O
                reaction_type = "combustion"
                reasoning = "Hydrocarbon combustion with oxygen"
                confidence = 0.5

            else:
                # Default: try to predict based on atom conservation
                products = self._predict_by_atom_conservation(reactants)
                reaction_type = "combination"
                reasoning = "Atom conservation-based prediction"
                confidence = 0.3
        elif len(reactants) == 1:
            # Decomposition or rearrangement
            products = [reactants[0]]  # No change
            reaction_type = "no_reaction"

        return PredictionResult(
            products=products,
            confidence=confidence,
            method="Heuristics",
            reaction_type=reaction_type,
            reasoning=reasoning
        )

    def _predict_by_atom_conservation(self, reactants: List[str]) -> List[str]:
        """Predict products while conserving atoms."""
        try:
            if not RDKIT_AVAILABLE:
                # Fallback: simple combination
                return ['.'.join(reactants)]

            # For now, return a simple combination that preserves atom count
            if len(reactants) == 2:
                # Try to form a single product that combines both reactants
                combined_smiles = f"{reactants[0]}.{reactants[1]}"
                return [combined_smiles]
            else:
                return ['.'.join(reactants)]

        except Exception:
            # Fallback to simple combination
            return ['.'.join(reactants)]

    def _load_reaction_templates(self) -> Dict[str, str]:
        """Load common reaction templates as SMARTS patterns."""
        
        return {
            "nucleophilic_substitution": "[C:1][Cl:2].[OH2:3]>>[C:1][OH:3].[Cl:2]",
            "esterification": "[C:1](=[O:2])[OH:3].[OH:4][C:5]>>[C:1](=[O:2])[O:4][C:5].[OH2:3]",
            "aldol_condensation": "[CH3:1][C:2](=[O:3])[H:4].[CH3:5][C:6](=[O:7])[H:8]>>[CH3:1][C:2](=[O:3])[CH2:4][C:6](=[O:7])[CH3:5]",
            "oxidation": "[C:1][OH:2].[O:3]=[O:4]>>[C:1]=[O:2].[OH2:3]",
            "reduction": "[C:1]=[O:2].[H:3][H:4]>>[C:1][OH:2]",
            "acid_base": "[C:1][COOH:2].[OH:3]>>[C:1][COO:2].[OH2:3]",
            "elimination": "[C:1][C:2]([Cl:3])[H:4]>>[C:1]=[C:2].[HCl:3]",
            "addition": "[C:1]=[C:2].[H:3][Cl:4]>>[C:1]([H:3])[C:2][Cl:4]",
            "hydrogen_combustion": "[H:1][H:2].[O:3]=[O:4]>>[O:3]([H:1])[H:2]",
            "methane_combustion": "[CH4:1].[O:2]=[O:3]>>[C:1]=[O:2].[O:3]",
            "hydrocarbon_combustion": "[C:1].[O:2]=[O:3]>>[C:1]=[O:2].[OH2:3]"
        }
    
    def _rate_limit(self):
        """Implement rate limiting for API calls."""
        current_time = time.time()
        time_since_last = current_time - self.last_api_call
        
        if time_since_last < self.min_api_interval:
            time.sleep(self.min_api_interval - time_since_last)
        
        self.last_api_call = time.time()
    
    def validate_products(self, products: List[str]) -> List[str]:
        """Validate and clean predicted products."""
        
        if not RDKIT_AVAILABLE:
            return products
        
        valid_products = []
        for smiles in products:
            try:
                mol = Chem.MolFromSmiles(smiles)
                if mol is not None:
                    # Canonicalize SMILES
                    canonical_smiles = Chem.MolToSmiles(mol)
                    valid_products.append(canonical_smiles)
            except:
                continue
        
        return valid_products


if __name__ == "__main__":
    # Example usage
    predictor = ProductPredictor()
    
    # Test prediction
    reactants = ["CCO", "CC(=O)O"]  # Ethanol + Acetic acid
    result = predictor.predict_products(reactants, use_llm=False)
    
    print(f"Reactants: {reactants}")
    print(f"Predicted products: {result.products}")
    print(f"Confidence: {result.confidence}")
    print(f"Method: {result.method}")
    print(f"Reaction type: {result.reaction_type}")
