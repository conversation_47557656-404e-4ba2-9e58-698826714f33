#!/usr/bin/env python3
"""
Debug the feasibility calculation for H2 + O2 -> H2O
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from product_predictor import ProductPredictor
from reaction_feasibility import ReactionFeasibilityChecker
from input_handler import InputHandler
from molecule_optimizer import MoleculeOptimizer

def debug_h2_o2_feasibility():
    """Debug the H2 + O2 feasibility calculation step by step"""
    print("🔍 Debugging H2 + O2 Feasibility Calculation")
    print("=" * 60)
    
    # Initialize components
    handler = InputHandler()
    predictor = ProductPredictor()
    checker = ReactionFeasibilityChecker()
    optimizer = MoleculeOptimizer()
    
    # 1. Parse reactants
    print("\n1. Parsing reactants:")
    h2_atoms = handler.parse_molecule('[H][H]')
    o2_atoms = handler.parse_molecule('O=O')
    print(f"   H2: {len(h2_atoms)} atoms")
    print(f"   O2: {len(o2_atoms)} atoms")
    
    # 2. Predict products
    print("\n2. Predicting products:")
    reactants_smiles = ['[H][H]', 'O=O']
    result = predictor.predict_products(reactants_smiles, temperature=600.0, use_llm=False)
    print(f"   Products: {result.products}")
    print(f"   Method: {result.method}")
    
    # 3. Parse products
    print("\n3. Parsing products:")
    product_atoms_list = []
    for product_smiles in result.products:
        atoms = handler.parse_molecule(product_smiles)
        product_atoms_list.append(atoms)
        print(f"   {product_smiles}: {len(atoms)} atoms")
    
    # 4. Optimize geometries
    print("\n4. Optimizing geometries:")
    
    print("   Optimizing H2...")
    h2_opt = optimizer.optimize_geometry(h2_atoms)
    print(f"   H2 converged: {h2_opt.converged}, Energy: {h2_opt.energy:.6f} eV")

    print("   Optimizing O2...")
    o2_opt = optimizer.optimize_geometry(o2_atoms)
    print(f"   O2 converged: {o2_opt.converged}, Energy: {o2_opt.energy:.6f} eV")

    product_opt_list = []
    for i, atoms in enumerate(product_atoms_list):
        print(f"   Optimizing product {i+1} ({result.products[i]})...")
        opt_result = optimizer.optimize_geometry(atoms)
        product_opt_list.append(opt_result)
        print(f"   Product {i+1} converged: {opt_result.converged}, Energy: {opt_result.energy:.6f} eV")
    
    # 5. Calculate energies manually
    print("\n5. Manual energy calculations:")
    
    # Get single point energies
    h2_energy = optimizer.single_point_energy(h2_opt.atoms)
    o2_energy = optimizer.single_point_energy(o2_opt.atoms)
    
    print(f"   H2 single point energy: {h2_energy:.6f} eV")
    print(f"   O2 single point energy: {o2_energy:.6f} eV")
    print(f"   Total reactant energy: {h2_energy + o2_energy:.6f} eV")
    
    total_product_energy = 0
    for i, opt_result in enumerate(product_opt_list):
        energy = optimizer.single_point_energy(opt_result.atoms)
        print(f"   Product {i+1} energy: {energy:.6f} eV")
        total_product_energy += energy
    
    print(f"   Total product energy: {total_product_energy:.6f} eV")
    
    # Calculate reaction energy
    delta_e = total_product_energy - (h2_energy + o2_energy)
    print(f"   ΔE = {delta_e:.6f} eV")
    print(f"   ΔE = {delta_e * 96.485:.2f} kJ/mol")  # Convert to kJ/mol
    
    # 6. Run full feasibility check
    print("\n6. Full feasibility check:")
    reactants_list = [h2_opt.atoms, o2_opt.atoms]
    
    try:
        feasibility_result = checker.check_feasibility(
            reactants=reactants_list,
            products=result.products,
            temperature=600.0,
            pressure=1.0
        )
        
        print(f"   Is feasible: {feasibility_result.is_feasible}")
        print(f"   Thermodynamic feasible: {feasibility_result.thermodynamic_feasible}")
        print(f"   Kinetic feasible: {feasibility_result.kinetic_feasible}")
        print(f"   ΔH: {feasibility_result.delta_h:.6f} eV")
        print(f"   ΔG: {feasibility_result.delta_g:.6f} eV")
        print(f"   Activation barrier: {feasibility_result.activation_barrier_estimate}")
        print(f"   Equilibrium constant: {feasibility_result.equilibrium_constant:.2e}")
        print(f"   Confidence: {feasibility_result.confidence:.3f}")
        print(f"   Recommendations: {feasibility_result.recommendations}")
        
        # Check feasibility criteria
        print("\n7. Feasibility criteria analysis:")
        print(f"   ΔG < 2.0 eV? {feasibility_result.delta_g < 2.0}")
        print(f"   K > 1e-12? {feasibility_result.equilibrium_constant > 1e-12}")
        print(f"   Thermodynamic criterion met? {feasibility_result.thermodynamic_feasible}")
        
        if feasibility_result.activation_barrier_estimate is not None:
            print(f"   Activation barrier < 4.0 eV? {feasibility_result.activation_barrier_estimate < 4.0}")
        print(f"   Kinetic criterion met? {feasibility_result.kinetic_feasible}")
        
    except Exception as e:
        print(f"   Feasibility check failed: {e}")
        import traceback
        traceback.print_exc()

def test_correct_stoichiometry():
    """Test with correct stoichiometry: 2H2 + O2 -> 2H2O"""
    print("\n\n🧪 Testing Correct Stoichiometry: 2H2 + O2")
    print("=" * 60)
    
    handler = InputHandler()
    predictor = ProductPredictor()
    checker = ReactionFeasibilityChecker()
    
    # Parse reactants with correct stoichiometry
    h2_1 = handler.parse_molecule('[H][H]')
    h2_2 = handler.parse_molecule('[H][H]')
    o2 = handler.parse_molecule('O=O')
    
    reactants_list = [h2_1, h2_2, o2]
    reactants_smiles = ['[H][H]', '[H][H]', 'O=O']
    
    # Predict products
    result = predictor.predict_products(reactants_smiles, temperature=600.0, use_llm=False)
    print(f"Products: {result.products}")
    
    # Check feasibility
    try:
        feasibility_result = checker.check_feasibility(
            reactants=reactants_list,
            products=result.products,
            temperature=600.0,
            pressure=1.0
        )
        
        print(f"Is feasible: {feasibility_result.is_feasible}")
        print(f"ΔG: {feasibility_result.delta_g:.6f} eV")
        print(f"Equilibrium constant: {feasibility_result.equilibrium_constant:.2e}")
        
    except Exception as e:
        print(f"Feasibility check failed: {e}")

if __name__ == "__main__":
    debug_h2_o2_feasibility()
    test_correct_stoichiometry()
