# Example configuration for Virtual Chemistry Simulation Lab
# Usage: python main.py --input-file example_config.yaml

# Simple hydrogen dissociation example
reactants:
  - "[H][H]"  # Hydrogen molecule

# Simulation parameters
temperature: 298.15  # K
pressure: 1.0        # atm
method: "PBE"        # DFT method
basis: "STO-3G"      # Basis set
neb_images: 5        # Number of NEB images
output_dir: "results/example"

# Optional settings
use_llm: false       # Disable LLM for this example
visualize_results: true
save_output: true
