#!/usr/bin/env python3
"""
Configuration Manager for Chemistry Lab

Manages different computational modes and settings for the chemistry lab.
Allows easy switching between ML surrogate models and traditional DFT calculations.

Author: AI Chemistry Lab
License: MIT
"""

import os
import yaml
import json
from typing import Dict, Any, Optional
from pathlib import Path
from dataclasses import dataclass, asdict


@dataclass
class ComputationConfig:
    """Configuration for computational methods."""
    # Surrogate model settings
    use_surrogate_models: bool = True
    fallback_to_dft: bool = True
    ml_confidence_threshold: float = 0.6
    
    # DFT settings
    dft_method: str = "PBE"
    basis_set: str = "6-31G*"
    force_tolerance: float = 0.05
    max_optimization_steps: int = 200
    
    # Performance settings
    enable_caching: bool = True
    cache_dir: str = "model_cache"
    parallel_calculations: bool = False
    max_workers: int = 4


@dataclass
class ReactionConfig:
    """Configuration for reaction conditions."""
    temperature: float = 298.15  # K
    pressure: float = 1.0  # atm
    solvent: str = "vacuum"
    catalyst: Optional[str] = None
    
    # Feasibility thresholds
    max_activation_barrier: float = 4.0  # eV
    min_equilibrium_constant: float = 1e-12
    
    # Prediction settings
    use_llm: bool = True
    llm_confidence_threshold: float = 0.7


@dataclass
class OutputConfig:
    """Configuration for output and visualization."""
    save_output: bool = True
    output_dir: str = "results"
    visualize_results: bool = True
    generate_reports: bool = True
    
    # Logging
    log_level: str = "INFO"
    log_file: Optional[str] = None


@dataclass
class LabConfig:
    """Complete laboratory configuration."""
    computation: ComputationConfig
    reaction: ReactionConfig
    output: OutputConfig
    
    # API keys
    openai_api_key: Optional[str] = None
    perplexity_api_key: Optional[str] = None


class ConfigManager:
    """Manages configuration for the chemistry lab."""
    
    def __init__(self, config_file: Optional[str] = None):
        """Initialize configuration manager."""
        self.config_file = config_file
        self.config = self._load_config()
    
    def _load_config(self) -> LabConfig:
        """Load configuration from file or create default."""
        if self.config_file and Path(self.config_file).exists():
            return self._load_from_file(self.config_file)
        else:
            return self._create_default_config()
    
    def _load_from_file(self, config_file: str) -> LabConfig:
        """Load configuration from YAML or JSON file."""
        config_path = Path(config_file)
        
        with open(config_path, 'r') as f:
            if config_path.suffix.lower() in ['.yaml', '.yml']:
                data = yaml.safe_load(f)
            elif config_path.suffix.lower() == '.json':
                data = json.load(f)
            else:
                raise ValueError(f"Unsupported config file format: {config_path.suffix}")
        
        # Convert to dataclass
        computation = ComputationConfig(**data.get('computation', {}))
        reaction = ReactionConfig(**data.get('reaction', {}))
        output = OutputConfig(**data.get('output', {}))
        
        return LabConfig(
            computation=computation,
            reaction=reaction,
            output=output,
            openai_api_key=data.get('openai_api_key'),
            perplexity_api_key=data.get('perplexity_api_key')
        )
    
    def _create_default_config(self) -> LabConfig:
        """Create default configuration."""
        return LabConfig(
            computation=ComputationConfig(),
            reaction=ReactionConfig(),
            output=OutputConfig(),
            openai_api_key=os.getenv('OPENAI_API_KEY'),
            perplexity_api_key=os.getenv('PERPLEXITY_API_KEY')
        )
    
    def save_config(self, filename: str, format: str = 'yaml'):
        """Save current configuration to file."""
        config_dict = asdict(self.config)
        
        with open(filename, 'w') as f:
            if format.lower() == 'yaml':
                yaml.dump(config_dict, f, default_flow_style=False, indent=2)
            elif format.lower() == 'json':
                json.dump(config_dict, f, indent=2)
            else:
                raise ValueError(f"Unsupported format: {format}")
    
    def get_ml_config(self) -> Dict[str, Any]:
        """Get configuration optimized for ML surrogate models."""
        config_dict = asdict(self.config)
        
        # Optimize for ML
        config_dict['computation']['use_surrogate_models'] = True
        config_dict['computation']['fallback_to_dft'] = False
        config_dict['computation']['enable_caching'] = True
        config_dict['reaction']['use_llm'] = False  # Focus on ML models
        
        return config_dict
    
    def get_dft_config(self) -> Dict[str, Any]:
        """Get configuration optimized for DFT calculations."""
        config_dict = asdict(self.config)
        
        # Optimize for DFT
        config_dict['computation']['use_surrogate_models'] = False
        config_dict['computation']['fallback_to_dft'] = True
        config_dict['computation']['force_tolerance'] = 0.01  # Tighter convergence
        
        return config_dict
    
    def get_hybrid_config(self) -> Dict[str, Any]:
        """Get configuration for hybrid ML+DFT approach."""
        config_dict = asdict(self.config)
        
        # Hybrid approach
        config_dict['computation']['use_surrogate_models'] = True
        config_dict['computation']['fallback_to_dft'] = True
        config_dict['computation']['ml_confidence_threshold'] = 0.7  # Higher threshold
        
        return config_dict
    
    def create_preset_configs(self):
        """Create preset configuration files."""
        presets = {
            'ml_fast': {
                'description': 'Fast ML-only predictions',
                'config': self.get_ml_config()
            },
            'dft_accurate': {
                'description': 'Accurate DFT calculations',
                'config': self.get_dft_config()
            },
            'hybrid_balanced': {
                'description': 'Balanced ML+DFT hybrid',
                'config': self.get_hybrid_config()
            }
        }
        
        for preset_name, preset_data in presets.items():
            filename = f"config_{preset_name}.yaml"
            
            # Add description as comment
            config_with_desc = {
                '_description': preset_data['description'],
                **preset_data['config']
            }
            
            with open(filename, 'w') as f:
                f.write(f"# {preset_data['description']}\n")
                yaml.dump(preset_data['config'], f, default_flow_style=False, indent=2)
            
            print(f"Created preset config: {filename}")


def create_example_configs():
    """Create example configuration files."""
    manager = ConfigManager()
    
    # Create preset configurations
    manager.create_preset_configs()
    
    # Create a comprehensive example config
    example_config = {
        'computation': {
            'use_surrogate_models': True,
            'fallback_to_dft': True,
            'ml_confidence_threshold': 0.6,
            'dft_method': 'PBE',
            'basis_set': '6-31G*',
            'enable_caching': True,
            'cache_dir': 'model_cache'
        },
        'reaction': {
            'temperature': 298.15,
            'pressure': 1.0,
            'solvent': 'vacuum',
            'max_activation_barrier': 4.0,
            'use_llm': True,
            'llm_confidence_threshold': 0.7
        },
        'output': {
            'save_output': True,
            'output_dir': 'results',
            'visualize_results': True,
            'log_level': 'INFO'
        },
        'openai_api_key': None,
        'perplexity_api_key': None
    }
    
    with open('config_example.yaml', 'w') as f:
        f.write("# Example configuration for Chemistry Lab\n")
        f.write("# Copy this file and modify as needed\n\n")
        yaml.dump(example_config, f, default_flow_style=False, indent=2)
    
    print("Created example config: config_example.yaml")


if __name__ == "__main__":
    # Create example configurations
    create_example_configs()
    
    # Test configuration loading
    manager = ConfigManager()
    print(f"Default config loaded:")
    print(f"  ML models: {manager.config.computation.use_surrogate_models}")
    print(f"  DFT fallback: {manager.config.computation.fallback_to_dft}")
    print(f"  Temperature: {manager.config.reaction.temperature} K")
    
    # Save default config
    manager.save_config('config_default.yaml')
    print("Saved default config: config_default.yaml")
