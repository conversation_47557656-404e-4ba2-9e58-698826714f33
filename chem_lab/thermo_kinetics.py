"""
Thermodynamics and Kinetics Module

Calculates thermodynamic properties (ΔH, ΔG, ΔS) and kinetic parameters
(rate constants, activation energies) for chemical reactions.

Author: AI Chemistry Lab
License: MIT
"""

import numpy as np
import warnings
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from ase import Atoms
from ase.thermochemistry import IdealGasThermo, HarmonicThermo

from reaction_pathway import PathwayResult


@dataclass
class ThermoKineticResult:
    """Container for thermodynamic and kinetic calculation results."""
    temperature: float  # K
    pressure: float  # atm
    delta_h: float  # eV
    delta_g: float  # eV
    delta_s: float  # eV/K
    activation_energy: float  # eV
    activation_enthalpy: float  # eV
    activation_entropy: float  # eV/K
    activation_free_energy: float  # eV
    rate_constant: float  # s⁻¹
    equilibrium_constant: float
    half_life: float  # s
    arrhenius_parameters: Dict[str, float]
    eyring_parameters: Dict[str, float]
    temperature_dependence: Dict[str, List[float]]


class ThermoKineticsCalculator:
    """
    Calculates thermodynamic and kinetic properties for chemical reactions.
    
    Includes:
    - Enthalpy, entropy, and Gibbs energy changes
    - Activation parameters
    - Rate constants (Arrhenius, Eyring)
    - Temperature and pressure effects
    - Equilibrium constants
    """
    
    def __init__(self):
        """Initialize the thermodynamics and kinetics calculator."""
        
        # Physical constants
        self.kb = 8.617333262e-5  # Boltzmann constant (eV/K)
        self.h = 4.135667696e-15  # Planck constant (eV·s)
        self.R = 8.31446261815324e-3  # Gas constant (kJ/mol/K)
        self.N_A = 6.02214076e23  # Avogadro's number
        
        # Standard conditions
        self.T_standard = 298.15  # K
        self.P_standard = 1.0  # atm
    
    def calculate_properties(self, pathway: PathwayResult,
                           temperature: float = 298.15,
                           pressure: float = 1.0) -> ThermoKineticResult:
        """
        Calculate thermodynamic and kinetic properties from reaction pathway.
        
        Args:
            pathway: Calculated reaction pathway
            temperature: Temperature (K)
            pressure: Pressure (atm)
            
        Returns:
            ThermoKineticResult object
        """
        
        print(f"🧮 Calculating thermodynamics and kinetics at {temperature} K, {pressure} atm")
        
        # Extract key structures
        reactants = pathway.images[0]
        products = pathway.images[-1]
        ts = pathway.images[pathway.transition_state_index]
        
        # Calculate thermodynamic properties
        delta_h, delta_s, delta_g = self._calculate_reaction_thermodynamics(
            reactants, products, temperature, pressure
        )
        
        # Calculate activation parameters
        act_energy, act_enthalpy, act_entropy, act_free_energy = self._calculate_activation_parameters(
            reactants, ts, temperature, pressure
        )
        
        # Calculate rate constant
        rate_constant = self._calculate_rate_constant(
            act_free_energy, temperature
        )
        
        # Calculate equilibrium constant
        equilibrium_constant = np.exp(-delta_g / (self.kb * temperature))
        
        # Calculate half-life
        half_life = 0.693 / rate_constant if rate_constant > 0 else float('inf')
        
        # Calculate Arrhenius parameters
        arrhenius_params = self._calculate_arrhenius_parameters(
            pathway, temperature
        )
        
        # Calculate Eyring parameters
        eyring_params = self._calculate_eyring_parameters(
            act_enthalpy, act_entropy
        )
        
        # Calculate temperature dependence
        temp_dependence = self._calculate_temperature_dependence(
            pathway, temperature, pressure
        )
        
        return ThermoKineticResult(
            temperature=temperature,
            pressure=pressure,
            delta_h=delta_h,
            delta_g=delta_g,
            delta_s=delta_s,
            activation_energy=act_energy,
            activation_enthalpy=act_enthalpy,
            activation_entropy=act_entropy,
            activation_free_energy=act_free_energy,
            rate_constant=rate_constant,
            equilibrium_constant=equilibrium_constant,
            half_life=half_life,
            arrhenius_parameters=arrhenius_params,
            eyring_parameters=eyring_params,
            temperature_dependence=temp_dependence
        )
    
    def _calculate_reaction_thermodynamics(self, reactants: Atoms, products: Atoms,
                                         temperature: float, pressure: float) -> Tuple[float, float, float]:
        """Calculate reaction thermodynamic properties."""
        
        # Calculate electronic energies
        e_reactants = reactants.get_potential_energy()
        e_products = products.get_potential_energy()
        delta_e = e_products - e_reactants
        
        # Estimate enthalpy change (simplified)
        delta_h = delta_e
        
        # Estimate entropy change (simplified)
        delta_s = self._estimate_entropy_change(reactants, products, temperature)
        
        # Calculate Gibbs energy change
        delta_g = delta_h - temperature * delta_s
        
        return delta_h, delta_s, delta_g
    
    def _calculate_activation_parameters(self, reactants: Atoms, ts: Atoms,
                                       temperature: float, pressure: float) -> Tuple[float, float, float, float]:
        """Calculate activation parameters."""
        
        # Calculate electronic energies
        e_reactants = reactants.get_potential_energy()
        e_ts = ts.get_potential_energy()
        act_energy = e_ts - e_reactants
        
        # Estimate activation enthalpy (simplified)
        act_enthalpy = act_energy
        
        # Estimate activation entropy (simplified)
        act_entropy = self._estimate_entropy_change(reactants, ts, temperature)
        
        # Calculate activation free energy
        act_free_energy = act_enthalpy - temperature * act_entropy
        
        return act_energy, act_enthalpy, act_entropy, act_free_energy
    
    def _estimate_entropy_change(self, initial: Atoms, final: Atoms,
                               temperature: float) -> float:
        """
        Estimate entropy change between two structures.
        
        This is a simplified approach. For accurate results, you would:
        1. Calculate vibrational frequencies
        2. Use statistical thermodynamics formulas
        3. Consider rotational and translational contributions
        """
        
        # Count atoms and molecules
        n_atoms_initial = len(initial)
        n_atoms_final = len(final)
        
        # Rough estimate based on structural complexity
        if n_atoms_final > n_atoms_initial:
            # More complex product, likely negative entropy change
            delta_s = -0.0001 * (n_atoms_final - n_atoms_initial) * temperature
        elif n_atoms_final < n_atoms_initial:
            # Simpler product, likely positive entropy change
            delta_s = 0.0001 * (n_atoms_initial - n_atoms_final) * temperature
        else:
            # Same complexity, estimate based on bond changes
            # This is very approximate
            delta_s = 0.00001 * temperature
        
        return delta_s
    
    def _calculate_rate_constant(self, act_free_energy: float,
                               temperature: float) -> float:
        """
        Calculate reaction rate constant using transition state theory.
        
        k = (kB*T/h) * exp(-ΔG‡/RT)
        """
        
        # Convert activation free energy to eV
        delta_g_act = act_free_energy  # Already in eV
        
        # Calculate rate constant (s⁻¹)
        prefactor = self.kb * temperature / self.h
        rate_constant = prefactor * np.exp(-delta_g_act / (self.kb * temperature))
        
        return rate_constant
    
    def _calculate_arrhenius_parameters(self, pathway: PathwayResult,
                                      temperature: float) -> Dict[str, float]:
        """
        Calculate Arrhenius parameters (A, Ea).
        
        k = A * exp(-Ea/RT)
        """
        
        # Activation energy from pathway
        ea = pathway.activation_energy  # eV
        
        # Estimate pre-exponential factor
        # For unimolecular reactions: A ~ 10^13 s⁻¹
        # For bimolecular reactions: A ~ 10^10 - 10^11 M⁻¹s⁻¹
        a_factor = 1e13  # s⁻¹ (simplified)
        
        # Calculate rate constant at reference temperature
        k_ref = a_factor * np.exp(-ea / (self.kb * temperature))
        
        return {
            'A': a_factor,
            'Ea': ea,
            'k_ref': k_ref
        }
    
    def _calculate_eyring_parameters(self, act_enthalpy: float,
                                   act_entropy: float) -> Dict[str, float]:
        """
        Calculate Eyring parameters (ΔH‡, ΔS‡).
        
        k = (kB*T/h) * exp(ΔS‡/R) * exp(-ΔH‡/RT)
        """
        
        return {
            'delta_H_double_dagger': act_enthalpy,
            'delta_S_double_dagger': act_entropy
        }
    
    def _calculate_temperature_dependence(self, pathway: PathwayResult,
                                        base_temp: float,
                                        pressure: float) -> Dict[str, List[float]]:
        """Calculate temperature dependence of kinetic and thermodynamic parameters."""
        
        # Temperature range
        temperatures = np.linspace(base_temp - 100, base_temp + 100, 11)
        
        # Initialize arrays
        rate_constants = []
        equilibrium_constants = []
        delta_g_values = []
        
        # Calculate properties at each temperature
        for temp in temperatures:
            # Skip negative or very low temperatures
            if temp <= 0:
                continue
                
            # Calculate activation free energy at this temperature
            act_energy = pathway.activation_energy
            act_entropy = self._estimate_entropy_change(
                pathway.images[0], 
                pathway.images[pathway.transition_state_index],
                temp
            )
            act_free_energy = act_energy - temp * act_entropy
            
            # Calculate rate constant
            rate_constant = self._calculate_rate_constant(act_free_energy, temp)
            rate_constants.append(rate_constant)
            
            # Calculate reaction free energy
            delta_h = pathway.reaction_energy
            delta_s = self._estimate_entropy_change(
                pathway.images[0], pathway.images[-1], temp
            )
            delta_g = delta_h - temp * delta_s
            delta_g_values.append(delta_g)
            
            # Calculate equilibrium constant
            eq_constant = np.exp(-delta_g / (self.kb * temp))
            equilibrium_constants.append(eq_constant)
        
        return {
            'temperatures': temperatures.tolist(),
            'rate_constants': rate_constants,
            'equilibrium_constants': equilibrium_constants,
            'delta_g_values': delta_g_values
        }
    
    def calculate_thermodynamics_from_energies(self, reactant_energy: float,
                                             product_energy: float,
                                             temperature: float = 298.15) -> Dict[str, float]:
        """
        Calculate thermodynamic properties from energies only.
        
        Args:
            reactant_energy: Energy of reactants (eV)
            product_energy: Energy of products (eV)
            temperature: Temperature (K)
            
        Returns:
            Dictionary with thermodynamic properties
        """
        
        # Calculate energy change
        delta_e = product_energy - reactant_energy
        
        # Estimate enthalpy change (simplified)
        delta_h = delta_e
        
        # Estimate entropy change (very rough approximation)
        delta_s = 0.0  # Without molecular information, assume zero
        
        # Calculate Gibbs energy change
        delta_g = delta_h - temperature * delta_s
        
        # Calculate equilibrium constant
        equilibrium_constant = np.exp(-delta_g / (self.kb * temperature))
        
        return {
            'delta_e': delta_e,
            'delta_h': delta_h,
            'delta_s': delta_s,
            'delta_g': delta_g,
            'equilibrium_constant': equilibrium_constant,
            'spontaneous': delta_g < 0
        }
    
    def calculate_rate_from_barrier(self, barrier: float,
                                  temperature: float = 298.15) -> Dict[str, float]:
        """
        Calculate rate constant from activation barrier.
        
        Args:
            barrier: Activation barrier (eV)
            temperature: Temperature (K)
            
        Returns:
            Dictionary with rate information
        """
        
        # Calculate rate constant using TST
        prefactor = self.kb * temperature / self.h
        rate_constant = prefactor * np.exp(-barrier / (self.kb * temperature))
        
        # Calculate half-life
        half_life = 0.693 / rate_constant if rate_constant > 0 else float('inf')
        
        return {
            'rate_constant': rate_constant,
            'half_life': half_life,
            'barrier': barrier,
            'temperature': temperature
        }


if __name__ == "__main__":
    # Example usage
    from input_handler import InputHandler
    from reaction_pathway import ReactionPathwayCalculator
    
    handler = InputHandler()
    pathway_calculator = ReactionPathwayCalculator()
    thermo_calculator = ThermoKineticsCalculator()
    
    try:
        # Simple test: H2 dissociation
        h2_coords = "H 0.0 0.0 0.0\nH 0.74 0.0 0.0"
        h2_separated = "H 0.0 0.0 0.0\nH 3.0 0.0 0.0"
        
        initial = handler.parse_molecule(h2_coords)
        final = handler.parse_molecule(h2_separated)
        
        # Calculate pathway
        pathway = pathway_calculator.calculate_pathway(
            [initial], ["[H]"], n_images=5, method="NEB"
        )
        
        # Calculate thermodynamics and kinetics
        result = thermo_calculator.calculate_properties(pathway)
        
        print(f"ΔH: {result.delta_h:.3f} eV")
        print(f"ΔG: {result.delta_g:.3f} eV")
        print(f"Activation energy: {result.activation_energy:.3f} eV")
        print(f"Rate constant: {result.rate_constant:.3e} s⁻¹")
        print(f"Half-life: {result.half_life:.3e} s")
        print(f"Equilibrium constant: {result.equilibrium_constant:.3e}")
        
    except Exception as e:
        print(f"Test failed: {e}")
