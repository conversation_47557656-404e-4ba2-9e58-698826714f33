# Configuration file for sample reactions
# This file can be used with: python main.py --input-file examples/config.yaml

# Example 1: Hydrogen dissociation
h2_dissociation:
  reactants:
    - "[H][H]"
  temperature: 298.15
  pressure: 1.0
  method: "PBE"
  basis: "STO-3G"
  neb_images: 5
  output_dir: "results/h2_dissociation"

# Example 2: Water formation
water_formation:
  reactants:
    - "[H][H]"
    - "O=O"
  temperature: 298.15
  pressure: 1.0
  method: "PBE"
  basis: "6-31G*"
  neb_images: 8
  output_dir: "results/water_formation"

# Example 3: Ethanol dehydration
ethanol_dehydration:
  reactants:
    - "CCO"
  temperature: 450.0
  pressure: 1.0
  method: "PBE"
  basis: "6-31G*"
  catalyst: "H+"
  neb_images: 10
  output_dir: "results/ethanol_dehydration"

# Example 4: Esterification
esterification:
  reactants:
    - "CC(=O)O"  # Acetic acid
    - "CCO"      # Ethanol
  temperature: 298.15
  pressure: 1.0
  method: "B3LYP"
  basis: "6-31G*"
  solvent: "water"
  neb_images: 8
  output_dir: "results/esterification"

# Example 5: Methane combustion
methane_combustion:
  reactants:
    - "C"        # Methane
    - "O=O"      # Oxygen
  temperature: 298.15
  pressure: 1.0
  method: "PBE"
  basis: "6-31G*"
  neb_images: 12
  output_dir: "results/methane_combustion"

# Default settings
default:
  temperature: 298.15
  pressure: 1.0
  method: "PBE"
  basis: "STO-3G"
  neb_images: 8
  max_force: 0.05
  use_llm: false
  visualize_results: true
  save_output: true
