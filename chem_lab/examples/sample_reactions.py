#!/usr/bin/env python3
"""
Sample Reactions Module

Provides example reactions for testing and demonstration purposes.
Includes dynamic test cases with configurable parameters.

Author: AI Chemistry Lab
License: MIT
"""

import os
import sys
import argparse
import yaml
import json
from typing import List, Dict, Any, Optional
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from input_handler import InputHandler
from product_predictor import ProductPredictor
from molecule_optimizer import MoleculeOptimizer
from reaction_feasibility import ReactionFeasibility<PERSON><PERSON><PERSON>
from reaction_pathway import ReactionPathwayCalculator
from thermo_kinetics import ThermoKineticsCalculator
from network_model import ReactionNetworkModel
from visualizer import ReactionVisualizer


class SampleReactions:
    """
    Collection of sample reactions for testing and demonstration.
    
    Features:
    - Predefined test cases
    - Dynamic reaction generation
    - Validation against known results
    - Comprehensive testing of all modules
    """
    
    def __init__(self):
        """Initialize the sample reactions."""
        self.handler = InputHandler()
        self.predictor = ProductPredictor()
        self.optimizer = MoleculeOptimizer()
        self.feasibility = ReactionFeasibilityChecker()
        self.pathway = ReactionPathwayCalculator()
        self.thermo = ThermoKineticsCalculator()
        self.network = ReactionNetworkModel()
        self.visualizer = ReactionVisualizer()
        
        # Define sample reactions
        self.sample_reactions = {
            'h2_dissociation': {
                'name': 'Hydrogen Dissociation',
                'reactants': ['H2'],
                'reactant_smiles': ['[H][H]'],
                'products': ['H', 'H'],
                'product_smiles': ['[H]', '[H]'],
                'temperature': 298.15,
                'pressure': 1.0,
                'expected_barrier': 4.5,  # eV (approximate)
                'expected_reaction_energy': 4.0,  # eV (approximate)
                'description': 'Dissociation of H2 into two hydrogen atoms'
            },
            'water_formation': {
                'name': 'Water Formation',
                'reactants': ['H2', 'O2'],
                'reactant_smiles': ['[H][H]', 'O=O'],
                'products': ['H2O'],
                'product_smiles': ['O'],
                'temperature': 298.15,
                'pressure': 1.0,
                'expected_barrier': 0.9,  # eV (approximate)
                'expected_reaction_energy': -2.5,  # eV (approximate)
                'description': 'Formation of water from hydrogen and oxygen'
            },
            'methane_combustion': {
                'name': 'Methane Combustion',
                'reactants': ['CH4', 'O2'],
                'reactant_smiles': ['C', 'O=O'],
                'products': ['CO2', 'H2O'],
                'product_smiles': ['C(=O)=O', 'O'],
                'temperature': 298.15,
                'pressure': 1.0,
                'expected_barrier': 1.2,  # eV (approximate)
                'expected_reaction_energy': -8.0,  # eV (approximate)
                'description': 'Combustion of methane with oxygen'
            },
            'ethanol_dehydration': {
                'name': 'Ethanol Dehydration',
                'reactants': ['C2H5OH'],
                'reactant_smiles': ['CCO'],
                'products': ['C2H4', 'H2O'],
                'product_smiles': ['C=C', 'O'],
                'temperature': 450.0,  # Higher temperature
                'pressure': 1.0,
                'expected_barrier': 1.8,  # eV (approximate)
                'expected_reaction_energy': 0.5,  # eV (approximate)
                'description': 'Dehydration of ethanol to ethylene'
            },
            'esterification': {
                'name': 'Esterification',
                'reactants': ['CH3COOH', 'C2H5OH'],
                'reactant_smiles': ['CC(=O)O', 'CCO'],
                'products': ['CH3COOC2H5', 'H2O'],
                'product_smiles': ['CC(=O)OCC', 'O'],
                'temperature': 298.15,
                'pressure': 1.0,
                'expected_barrier': 1.5,  # eV (approximate)
                'expected_reaction_energy': -0.2,  # eV (approximate)
                'description': 'Esterification of acetic acid with ethanol'
            }
        }
    
    def get_reaction(self, reaction_id: str) -> Dict[str, Any]:
        """Get a specific reaction by ID."""
        if reaction_id in self.sample_reactions:
            return self.sample_reactions[reaction_id]
        else:
            raise ValueError(f"Unknown reaction ID: {reaction_id}")
    
    def list_reactions(self) -> List[Dict[str, Any]]:
        """List all available sample reactions."""
        return [
            {
                'id': reaction_id,
                'name': reaction['name'],
                'description': reaction['description'],
                'reactants': reaction['reactant_smiles'],
                'products': reaction['product_smiles']
            }
            for reaction_id, reaction in self.sample_reactions.items()
        ]
    
    def run_reaction(self, reaction_id: str, 
                   output_dir: Optional[str] = None,
                   visualize: bool = True) -> Dict[str, Any]:
        """
        Run a complete simulation for a sample reaction.
        
        Args:
            reaction_id: ID of the reaction to run
            output_dir: Directory to save results
            visualize: Whether to create visualizations
            
        Returns:
            Dictionary with simulation results
        """
        
        # Get reaction data
        reaction = self.get_reaction(reaction_id)
        
        print(f"🧪 Running sample reaction: {reaction['name']}")
        print(f"📝 {reaction['description']}")
        print(f"🔍 Reactants: {reaction['reactant_smiles']}")
        print(f"🎯 Expected products: {reaction['product_smiles']}")
        
        # Create output directory
        if output_dir:
            output_path = Path(output_dir)
            output_path.mkdir(exist_ok=True)
        
        # Step 1: Parse reactants
        print("\n1️⃣ Parsing reactants...")
        reactant_atoms = []
        for smiles in reaction['reactant_smiles']:
            try:
                atoms = self.handler.parse_molecule(smiles)
                reactant_atoms.append(atoms)
                print(f"✅ Parsed {smiles}: {atoms.get_chemical_formula()}")
            except Exception as e:
                print(f"❌ Failed to parse {smiles}: {e}")
        
        # Step 2: Predict products
        print("\n2️⃣ Predicting products...")
        try:
            prediction = self.predictor.predict_products(
                reaction['reactant_smiles'],
                temperature=reaction['temperature'],
                pressure=reaction['pressure'],
                use_llm=False  # Disable LLM for testing
            )
            print(f"✅ Predicted products: {prediction.products}")
            print(f"📊 Confidence: {prediction.confidence:.2f}")
            print(f"🔍 Method: {prediction.method}")
        except Exception as e:
            print(f"❌ Product prediction failed: {e}")
            prediction = None
        
        # Step 3: Optimize geometries
        print("\n3️⃣ Optimizing geometries...")
        optimized_reactants = []
        for i, atoms in enumerate(reactant_atoms):
            try:
                result = self.optimizer.optimize_geometry(atoms)
                optimized_reactants.append(result.atoms)
                print(f"✅ Optimized reactant {i+1}: {result.converged}")
                print(f"📊 Energy: {result.energy:.6f} eV")
            except Exception as e:
                print(f"❌ Optimization failed for reactant {i+1}: {e}")
                optimized_reactants.append(atoms)  # Use unoptimized
        
        # Step 4: Check feasibility
        print("\n4️⃣ Checking reaction feasibility...")
        try:
            feasibility = self.feasibility.check_feasibility(
                optimized_reactants,
                reaction['product_smiles'],
                temperature=reaction['temperature'],
                pressure=reaction['pressure']
            )
            print(f"✅ Reaction feasible: {feasibility.is_feasible}")
            print(f"📊 ΔG: {feasibility.delta_g:.3f} eV")
            print(f"📊 ΔH: {feasibility.delta_h:.3f} eV")
            print(f"📊 Activation barrier: {feasibility.activation_barrier_estimate}")
        except Exception as e:
            print(f"❌ Feasibility check failed: {e}")
            feasibility = None
        
        # Step 5: Calculate reaction pathway
        print("\n5️⃣ Calculating reaction pathway...")
        try:
            pathway = self.pathway.calculate_pathway(
                optimized_reactants,
                reaction['product_smiles'],
                n_images=5,  # Reduced for testing
                method="NEB"
            )
            print(f"✅ Pathway calculated: {pathway.converged}")
            print(f"📊 Activation energy: {pathway.activation_energy:.3f} eV")
            print(f"📊 Reaction energy: {pathway.reaction_energy:.3f} eV")
            
            # Validate against expected values
            activation_error = abs(pathway.activation_energy - reaction['expected_barrier'])
            reaction_error = abs(pathway.reaction_energy - reaction['expected_reaction_energy'])
            
            print(f"📏 Activation energy error: {activation_error:.3f} eV")
            print(f"📏 Reaction energy error: {reaction_error:.3f} eV")
            
        except Exception as e:
            print(f"❌ Pathway calculation failed: {e}")
            pathway = None
        
        # Step 6: Calculate thermodynamics and kinetics
        print("\n6️⃣ Calculating thermodynamics and kinetics...")
        if pathway:
            try:
                thermo = self.thermo.calculate_properties(
                    pathway,
                    temperature=reaction['temperature'],
                    pressure=reaction['pressure']
                )
                print(f"✅ Thermodynamics calculated")
                print(f"📊 ΔG: {thermo.delta_g:.3f} eV")
                print(f"📊 Rate constant: {thermo.rate_constant:.3e} s⁻¹")
                print(f"📊 Half-life: {thermo.half_life:.3e} s")
            except Exception as e:
                print(f"❌ Thermodynamics calculation failed: {e}")
                thermo = None
        else:
            thermo = None
        
        # Step 7: Build reaction network
        print("\n7️⃣ Building reaction network...")
        if pathway:
            try:
                network = self.network.build_network(
                    optimized_reactants,
                    reaction['product_smiles'],
                    pathway,
                    network_id=reaction_id
                )
                print(f"✅ Network built: {network.properties['n_nodes']} nodes, {network.properties['n_edges']} edges")
            except Exception as e:
                print(f"❌ Network building failed: {e}")
                network = None
        else:
            network = None
        
        # Step 8: Create visualizations
        if visualize and pathway and output_dir:
            print("\n8️⃣ Creating visualizations...")
            try:
                # Energy diagram
                energy_path = output_path / f"{reaction_id}_energy.png"
                self.visualizer.plot_energy_diagram(
                    pathway, thermo, str(energy_path), interactive=False
                )
                print(f"✅ Energy diagram saved: {energy_path}")
                
                # Network visualization
                if network:
                    network_path = output_path / f"{reaction_id}_network.png"
                    self.visualizer.plot_reaction_network(
                        network, str(network_path)
                    )
                    print(f"✅ Network visualization saved: {network_path}")
                
                # Summary report
                summary_path = output_path / f"{reaction_id}_summary.png"
                self.visualizer.create_summary_report(
                    pathway, thermo, network, str(summary_path)
                )
                print(f"✅ Summary report saved: {summary_path}")
                
            except Exception as e:
                print(f"❌ Visualization failed: {e}")
        
        # Compile results
        results = {
            'reaction': reaction,
            'prediction': prediction,
            'feasibility': feasibility,
            'pathway': pathway,
            'thermodynamics': thermo,
            'network': network
        }
        
        # Save results
        if output_dir:
            try:
                # Save basic results as JSON
                results_path = output_path / f"{reaction_id}_results.json"
                with open(results_path, 'w') as f:
                    json.dump({
                        'reaction_id': reaction_id,
                        'name': reaction['name'],
                        'reactants': reaction['reactant_smiles'],
                        'products': reaction['product_smiles'],
                        'temperature': reaction['temperature'],
                        'pressure': reaction['pressure'],
                        'activation_energy': pathway.activation_energy if pathway else None,
                        'reaction_energy': pathway.reaction_energy if pathway else None,
                        'delta_g': thermo.delta_g if thermo else None,
                        'rate_constant': thermo.rate_constant if thermo else None,
                        'is_feasible': feasibility.is_feasible if feasibility else None
                    }, f, indent=2)
                print(f"✅ Results saved: {results_path}")
            except Exception as e:
                print(f"❌ Failed to save results: {e}")
        
        return results
    
    def create_custom_reaction(self, reactants: List[str], 
                             products: Optional[List[str]] = None,
                             temperature: float = 298.15,
                             pressure: float = 1.0,
                             name: str = "Custom Reaction") -> Dict[str, Any]:
        """
        Create a custom reaction from user-provided reactants.
        
        Args:
            reactants: List of reactant SMILES
            products: Optional list of product SMILES
            temperature: Reaction temperature (K)
            pressure: Reaction pressure (atm)
            name: Reaction name
            
        Returns:
            Dictionary with reaction data
        """
        
        # Generate a unique ID
        reaction_id = f"custom_{abs(hash(''.join(reactants)))}"
        
        # Predict products if not provided
        if not products:
            try:
                prediction = self.predictor.predict_products(
                    reactants, temperature, pressure
                )
                products = prediction.products
            except:
                products = []
        
        # Create reaction data
        reaction = {
            'name': name,
            'reactants': [r.replace('=', '') for r in reactants],  # Simplified names
            'reactant_smiles': reactants,
            'products': [p.replace('=', '') for p in products],  # Simplified names
            'product_smiles': products,
            'temperature': temperature,
            'pressure': pressure,
            'expected_barrier': None,  # Unknown
            'expected_reaction_energy': None,  # Unknown
            'description': f"Custom reaction: {' + '.join(reactants)} → {' + '.join(products)}"
        }
        
        # Add to sample reactions
        self.sample_reactions[reaction_id] = reaction
        
        return reaction


def main():
    """Command-line interface for sample reactions."""
    parser = argparse.ArgumentParser(description='Run sample chemical reactions')
    
    parser.add_argument('--list', action='store_true',
                      help='List all available sample reactions')
    
    parser.add_argument('--reaction', type=str,
                      help='ID of the reaction to run')
    
    parser.add_argument('--reactants', nargs='+', type=str,
                      help='Custom reactants (SMILES strings)')
    
    parser.add_argument('--products', nargs='+', type=str,
                      help='Custom products (SMILES strings)')
    
    parser.add_argument('--temperature', type=float, default=298.15,
                      help='Reaction temperature in K (default: 298.15)')
    
    parser.add_argument('--pressure', type=float, default=1.0,
                      help='Reaction pressure in atm (default: 1.0)')
    
    parser.add_argument('--output-dir', type=str, default='results',
                      help='Directory to save results (default: results)')
    
    parser.add_argument('--no-visualize', action='store_true',
                      help='Disable visualization generation')
    
    args = parser.parse_args()
    
    # Initialize sample reactions
    samples = SampleReactions()
    
    # List available reactions
    if args.list:
        print("Available sample reactions:")
        for i, reaction in enumerate(samples.list_reactions()):
            print(f"{i+1}. {reaction['id']}: {reaction['name']}")
            print(f"   {reaction['description']}")
            print(f"   Reactants: {reaction['reactants']}")
            print(f"   Products: {reaction['products']}")
            print()
        return
    
    # Run custom reaction
    if args.reactants:
        print(f"Creating custom reaction with reactants: {args.reactants}")
        reaction = samples.create_custom_reaction(
            args.reactants,
            args.products,
            args.temperature,
            args.pressure
        )
        reaction_id = list(samples.sample_reactions.keys())[-1]
        print(f"Created reaction ID: {reaction_id}")
        
        # Run the custom reaction
        results = samples.run_reaction(
            reaction_id,
            args.output_dir,
            not args.no_visualize
        )
        return
    
    # Run specific reaction
    if args.reaction:
        try:
            results = samples.run_reaction(
                args.reaction,
                args.output_dir,
                not args.no_visualize
            )
            print("\n✅ Reaction simulation completed successfully!")
        except Exception as e:
            print(f"\n❌ Reaction simulation failed: {e}")
        return
    
    # If no specific action, show help
    parser.print_help()


if __name__ == "__main__":
    main()
