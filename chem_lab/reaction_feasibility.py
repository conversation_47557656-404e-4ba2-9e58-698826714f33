"""
Reaction Feasibility Module

Checks thermodynamic and kinetic feasibility of chemical reactions.
Includes energy balance, activation barrier estimates, and reaction conditions analysis.

Author: AI Chemistry Lab
License: MIT
"""

import numpy as np
import warnings
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from ase import Atoms

from molecule_optimizer import MoleculeOptimizer, OptimizationResult

# Import surrogate models
try:
    from surrogate_models import SurrogateModelManager, ReactionFeatures
    SURROGATE_AVAILABLE = True
except ImportError:
    SURROGATE_AVAILABLE = False
    warnings.warn("Surrogate models not available. Falling back to DFT calculations.")


@dataclass
class FeasibilityResult:
    """Container for reaction feasibility analysis results."""
    is_feasible: bool
    thermodynamic_feasible: bool
    kinetic_feasible: bool
    delta_h: float  # Enthalpy change (eV)
    delta_g: float  # Gibbs energy change (eV)
    activation_barrier_estimate: Optional[float]  # eV
    equilibrium_constant: float
    reaction_rate_estimate: Optional[float]  # s⁻¹
    temperature_range: Tuple[float, float]  # K
    pressure_sensitivity: str
    solvent_effects: Dict[str, Any]
    recommendations: List[str]
    confidence: float
    # New fields for surrogate model integration
    ml_predictions: Optional[Dict[str, Any]] = None
    computation_method: str = "DFT"  # "DFT", "ML", or "Hybrid"
    prediction_uncertainties: Optional[Dict[str, float]] = None


class ReactionFeasibilityChecker:
    """
    Analyzes reaction feasibility from thermodynamic and kinetic perspectives.
    
    Performs:
    - Energy balance calculations
    - Activation barrier estimation
    - Temperature and pressure effects
    - Solvent influence assessment
    - Reaction rate estimation
    """
    
    def __init__(self, use_surrogate_models: bool = True, fallback_to_dft: bool = True):
        """Initialize the feasibility checker."""
        self.optimizer = MoleculeOptimizer()
        self.use_surrogate_models = use_surrogate_models and SURROGATE_AVAILABLE
        self.fallback_to_dft = fallback_to_dft

        # Initialize surrogate model manager
        if self.use_surrogate_models:
            try:
                self.surrogate_manager = SurrogateModelManager()
                print("✅ Surrogate models initialized for feasibility checking")
            except Exception as e:
                warnings.warn(f"Failed to initialize surrogate models: {e}")
                self.use_surrogate_models = False

        # Physical constants
        self.R = 8.314e-3  # Gas constant (kJ/mol/K)
        self.kb = 8.617e-5  # Boltzmann constant (eV/K)
        self.h = 4.136e-15  # Planck constant (eV·s)

        # Feasibility thresholds (more realistic)
        self.max_activation_barrier = 4.0  # eV (~400 kJ/mol) - Many real reactions have high barriers
        self.min_equilibrium_constant = 1e-12  # Much more permissive
        self.standard_temperature = 298.15  # K
        self.standard_pressure = 1.0  # atm
    
    def check_feasibility(self, reactants: List[Atoms],
                         products: List[str],
                         temperature: float = 298.15,
                         pressure: float = 1.0,
                         solvent: str = "vacuum",
                         catalyst: Optional[str] = None) -> FeasibilityResult:
        """
        Comprehensive feasibility analysis using ML models with DFT fallback.

        Args:
            reactants: List of optimized reactant Atoms objects
            products: List of product SMILES strings
            temperature: Reaction temperature (K)
            pressure: Reaction pressure (atm)
            solvent: Solvent name
            catalyst: Optional catalyst SMILES

        Returns:
            FeasibilityResult object
        """

        print(f"🔍 Analyzing reaction feasibility at {temperature} K, {pressure} atm")

        # Try ML-based prediction first
        if self.use_surrogate_models:
            try:
                return self._check_feasibility_with_ml(
                    reactants, products, temperature, pressure, solvent, catalyst
                )
            except Exception as e:
                warnings.warn(f"ML feasibility check failed: {e}")
                if not self.fallback_to_dft:
                    return self._create_infeasible_result(f"ML prediction failed: {e}")
                print("🔄 Falling back to DFT calculations...")

        # Fallback to original DFT-based method
        return self._check_feasibility_with_dft(
            reactants, products, temperature, pressure, solvent, catalyst
        )

    def _check_feasibility_with_ml(self, reactants: List[Atoms], products: List[str],
                                  temperature: float, pressure: float,
                                  solvent: str, catalyst: Optional[str]) -> FeasibilityResult:
        """Check feasibility using ML surrogate models."""

        # Convert Atoms objects to SMILES for ML models
        reactant_smiles = self._atoms_to_smiles(reactants)

        # Extract features for ML models
        features = self.surrogate_manager.extract_reaction_features(
            reactants=reactant_smiles,
            products=products,
            temperature=temperature,
            pressure=pressure,
            solvent=solvent,
            catalyst=catalyst
        )

        # Get ML predictions
        ml_predictions = {}
        uncertainties = {}

        # Feasibility prediction
        feasibility_result = self.surrogate_manager.predict_feasibility(features)
        ml_predictions['feasibility'] = feasibility_result
        uncertainties['feasibility'] = feasibility_result.uncertainty

        # Activation energy prediction
        activation_result = self.surrogate_manager.predict_activation_energy(features)
        ml_predictions['activation_energy'] = activation_result
        uncertainties['activation_energy'] = getattr(activation_result, 'uncertainty', None)

        # Yield prediction (for additional confidence)
        yield_result = self.surrogate_manager.predict_yield(features)
        ml_predictions['yield'] = yield_result

        # Determine feasibility based on ML predictions
        is_feasible = feasibility_result.prediction
        activation_barrier = activation_result.prediction
        predicted_yield = yield_result.prediction

        # Calculate thermodynamic properties using ML-informed estimates
        thermo_result = self._estimate_thermodynamics_from_ml(
            ml_predictions, temperature, pressure
        )

        # Kinetic analysis
        kinetic_feasible = (
            activation_barrier is None or
            activation_barrier < self.max_activation_barrier
        )

        # Overall feasibility (more permissive with ML)
        overall_feasible = (
            is_feasible and
            (thermo_result['delta_g'] < 3.0 or  # More permissive for ML
             thermo_result['equilibrium_constant'] > self.min_equilibrium_constant) and
            kinetic_feasible
        )

        # Generate recommendations
        recommendations = self._generate_ml_recommendations(
            ml_predictions, temperature, pressure, catalyst
        )

        # Calculate confidence (weighted average of model confidences)
        confidence = self._calculate_ml_confidence(ml_predictions)

        return FeasibilityResult(
            is_feasible=overall_feasible,
            thermodynamic_feasible=thermo_result['delta_g'] < 2.0,
            kinetic_feasible=kinetic_feasible,
            delta_h=thermo_result['delta_h'],
            delta_g=thermo_result['delta_g'],
            activation_barrier_estimate=activation_barrier,
            equilibrium_constant=thermo_result['equilibrium_constant'],
            reaction_rate_estimate=self._estimate_rate_from_ml(activation_barrier, temperature),
            temperature_range=self._estimate_temperature_range(thermo_result),
            pressure_sensitivity="low",  # Default for ML predictions
            solvent_effects={'solvent': solvent, 'effect': 'estimated'},
            recommendations=recommendations,
            confidence=confidence,
            ml_predictions=ml_predictions,
            computation_method="ML",
            prediction_uncertainties=uncertainties
        )

    def _atoms_to_smiles(self, atoms_list: List[Atoms]) -> List[str]:
        """Convert ASE Atoms objects to SMILES strings."""
        smiles_list = []

        try:
            from rdkit import Chem
            from rdkit.Chem import rdMolFiles

            for atoms in atoms_list:
                try:
                    # Write atoms to temporary xyz file
                    import tempfile
                    with tempfile.NamedTemporaryFile(mode='w', suffix='.xyz', delete=False) as f:
                        atoms.write(f.name)

                    # Read with RDKit and convert to SMILES
                    mol = Chem.MolFromXYZFile(f.name)
                    if mol:
                        smiles = Chem.MolToSmiles(mol)
                        smiles_list.append(smiles)
                    else:
                        # Fallback: simple heuristic based on atomic composition
                        smiles_list.append(self._atoms_to_simple_smiles(atoms))

                except Exception as e:
                    warnings.warn(f"Failed to convert atoms to SMILES: {e}")
                    smiles_list.append(self._atoms_to_simple_smiles(atoms))

        except ImportError:
            # Fallback without RDKit
            for atoms in atoms_list:
                smiles_list.append(self._atoms_to_simple_smiles(atoms))

        return smiles_list

    def _atoms_to_simple_smiles(self, atoms: Atoms) -> str:
        """Simple fallback to convert atoms to basic SMILES-like representation."""
        symbols = atoms.get_chemical_symbols()

        # Handle common simple molecules
        if len(symbols) == 2 and symbols == ['H', 'H']:
            return '[H][H]'
        elif len(symbols) == 2 and symbols == ['O', 'O']:
            return 'O=O'
        elif len(symbols) == 3 and symbols.count('H') == 2 and symbols.count('O') == 1:
            return 'O'  # Water
        elif len(symbols) == 2 and symbols.count('C') == 1 and symbols.count('O') == 1:
            return 'C=O'  # CO
        else:
            # Generic representation
            unique_elements = list(set(symbols))
            return '.'.join(unique_elements)

    def _estimate_thermodynamics_from_ml(self, ml_predictions: Dict[str, Any],
                                       temperature: float, pressure: float) -> Dict[str, float]:
        """Estimate thermodynamic properties from ML predictions."""

        # Get yield and feasibility predictions
        yield_pred = ml_predictions.get('yield', {}).prediction if 'yield' in ml_predictions else 0.5
        feasible = ml_predictions.get('feasibility', {}).prediction if 'feasibility' in ml_predictions else True

        # Estimate thermodynamic properties based on ML predictions
        if feasible and yield_pred > 0.5:
            # Favorable reaction
            delta_h = -2.0 - (yield_pred - 0.5) * 4.0  # -2 to -4 eV
            delta_g = delta_h + 0.5  # Slightly less favorable Gibbs energy
        elif feasible:
            # Marginally favorable
            delta_h = -0.5 - yield_pred * 2.0  # -0.5 to -1.5 eV
            delta_g = delta_h + 0.3
        else:
            # Unfavorable
            delta_h = 1.0 + (1.0 - yield_pred) * 2.0  # 1 to 3 eV
            delta_g = delta_h + 0.5

        # Calculate equilibrium constant
        equilibrium_constant = np.exp(-delta_g / (self.kb * temperature))

        return {
            'delta_h': delta_h,
            'delta_g': delta_g,
            'equilibrium_constant': equilibrium_constant,
            'delta_s': 0.0  # Simplified
        }

    def _estimate_rate_from_ml(self, activation_energy: Optional[float],
                              temperature: float) -> Optional[float]:
        """Estimate reaction rate from ML-predicted activation energy."""
        if activation_energy is None:
            return None

        # Arrhenius equation: k = A * exp(-Ea/kT)
        A = 1e13  # Pre-exponential factor (s^-1)
        rate_constant = A * np.exp(-activation_energy / (self.kb * temperature))
        return rate_constant

    def _generate_ml_recommendations(self, ml_predictions: Dict[str, Any],
                                   temperature: float, pressure: float,
                                   catalyst: Optional[str]) -> List[str]:
        """Generate recommendations based on ML predictions."""
        recommendations = []

        # Get predictions
        feasible = ml_predictions.get('feasibility', {}).prediction if 'feasibility' in ml_predictions else True
        yield_pred = ml_predictions.get('yield', {}).prediction if 'yield' in ml_predictions else 0.5
        activation = ml_predictions.get('activation_energy', {}).prediction if 'activation_energy' in ml_predictions else 1.0

        if not feasible:
            recommendations.append("ML model predicts low reaction feasibility")
            recommendations.append("Consider alternative reaction conditions or pathways")

        if yield_pred < 0.3:
            recommendations.append("Low predicted yield - optimize reaction conditions")

        if activation > 2.0:
            recommendations.append("High activation barrier predicted")
            if not catalyst:
                recommendations.append("Consider adding a catalyst to reduce activation energy")

        if temperature < 298:
            recommendations.append("Consider higher temperature for better kinetics")
        elif temperature > 600:
            recommendations.append("High temperature may cause side reactions")

        return recommendations

    def _calculate_ml_confidence(self, ml_predictions: Dict[str, Any]) -> float:
        """Calculate overall confidence from ML model predictions."""
        confidences = []

        for pred_name, pred_result in ml_predictions.items():
            if hasattr(pred_result, 'confidence'):
                confidences.append(pred_result.confidence)

        if not confidences:
            return 0.5  # Default moderate confidence

        # Weighted average (could be improved with model-specific weights)
        return np.mean(confidences)

    def _check_feasibility_with_dft(self, reactants: List[Atoms], products: List[str],
                                   temperature: float, pressure: float,
                                   solvent: str, catalyst: Optional[str]) -> FeasibilityResult:
        """Original DFT-based feasibility checking method."""

        # Convert products to Atoms objects and optimize
        from input_handler import InputHandler
        handler = InputHandler()

        product_atoms = []
        for product_smiles in products:
            try:
                atoms = handler.parse_molecule(product_smiles)
                opt_result = self.optimizer.optimize_geometry(atoms)
                if opt_result.converged:
                    product_atoms.append(opt_result.atoms)
                else:
                    warnings.warn(f"Product optimization failed: {product_smiles}")
                    product_atoms.append(atoms)  # Use unoptimized
            except Exception as e:
                warnings.warn(f"Could not process product {product_smiles}: {e}")
                continue

        if not product_atoms:
            return self._create_infeasible_result("No valid products found")

        # Calculate thermodynamic properties
        thermo_result = self._analyze_thermodynamics(
            reactants, product_atoms, temperature, pressure
        )

        # Estimate kinetic barriers
        kinetic_result = self._analyze_kinetics(
            reactants, product_atoms, temperature, catalyst
        )

        # Analyze environmental effects
        solvent_effects = self._analyze_solvent_effects(
            reactants, product_atoms, solvent
        )

        # Determine overall feasibility (more realistic criteria)
        thermodynamic_feasible = (
            thermo_result['delta_g'] < 2.0 or  # Allow endergonic reactions up to 2 eV
            thermo_result['equilibrium_constant'] > self.min_equilibrium_constant
        )

        kinetic_feasible = (
            kinetic_result['activation_barrier'] is None or
            kinetic_result['activation_barrier'] < self.max_activation_barrier
        )

        # A reaction is feasible if it's either thermodynamically favorable OR kinetically accessible
        # (not requiring both - many real reactions are driven by conditions)
        is_feasible = thermodynamic_feasible or (
            kinetic_feasible and abs(thermo_result['delta_g']) < 5.0  # Reasonable energy range
        )

        # Generate recommendations
        recommendations = self._generate_recommendations(
            thermo_result, kinetic_result, solvent_effects,
            temperature, pressure, catalyst
        )

        # Estimate confidence
        confidence = self._calculate_confidence(
            thermo_result, kinetic_result, len(reactants), len(product_atoms)
        )

        return FeasibilityResult(
            is_feasible=is_feasible,
            thermodynamic_feasible=thermodynamic_feasible,
            kinetic_feasible=kinetic_feasible,
            delta_h=thermo_result['delta_h'],
            delta_g=thermo_result['delta_g'],
            activation_barrier_estimate=kinetic_result['activation_barrier'],
            equilibrium_constant=thermo_result['equilibrium_constant'],
            reaction_rate_estimate=kinetic_result['rate_estimate'],
            temperature_range=self._estimate_temperature_range(thermo_result),
            pressure_sensitivity=self._assess_pressure_sensitivity(reactants, product_atoms),
            solvent_effects=solvent_effects,
            recommendations=recommendations,
            confidence=confidence,
            ml_predictions=None,
            computation_method="DFT",
            prediction_uncertainties=None
        )
    
    def _analyze_thermodynamics(self, reactants: List[Atoms], 
                              products: List[Atoms],
                              temperature: float, 
                              pressure: float) -> Dict[str, float]:
        """Analyze thermodynamic feasibility."""
        
        # Calculate energies
        reactant_energies = []
        product_energies = []
        
        for atoms in reactants:
            try:
                energy = self.optimizer.single_point_energy(atoms)
                reactant_energies.append(energy)
            except:
                reactant_energies.append(0.0)  # Fallback
        
        for atoms in products:
            try:
                energy = self.optimizer.single_point_energy(atoms)
                product_energies.append(energy)
            except:
                product_energies.append(0.0)  # Fallback
        
        # Calculate reaction energy
        delta_e = sum(product_energies) - sum(reactant_energies)
        
        # Estimate enthalpy change (simplified)
        delta_h = delta_e  # Assume ΔH ≈ ΔE for condensed phases
        
        # Estimate entropy change (very rough approximation)
        delta_s_estimate = self._estimate_entropy_change(reactants, products)
        
        # Calculate Gibbs energy change
        delta_g = delta_h - temperature * delta_s_estimate / 1000  # Convert J to eV
        
        # Calculate equilibrium constant
        equilibrium_constant = np.exp(-delta_g / (self.kb * temperature))
        
        return {
            'delta_e': delta_e,
            'delta_h': delta_h,
            'delta_g': delta_g,
            'delta_s': delta_s_estimate,
            'equilibrium_constant': equilibrium_constant
        }
    
    def _analyze_kinetics(self, reactants: List[Atoms], 
                         products: List[Atoms],
                         temperature: float,
                         catalyst: Optional[str]) -> Dict[str, Any]:
        """Analyze kinetic feasibility."""
        
        # Estimate activation barrier using empirical correlations
        activation_barrier = self._estimate_activation_barrier(
            reactants, products, catalyst
        )
        
        # Estimate reaction rate
        if activation_barrier is not None:
            # Arrhenius equation: k = A * exp(-Ea/kT)
            # Assume pre-exponential factor A ~ 10^13 s^-1
            A = 1e13  # s^-1
            rate_constant = A * np.exp(-activation_barrier / (self.kb * temperature))
        else:
            rate_constant = None
        
        return {
            'activation_barrier': activation_barrier,
            'rate_estimate': rate_constant,
            'half_life': 0.693 / rate_constant if rate_constant else None
        }
    
    def _estimate_activation_barrier(self, reactants: List[Atoms], 
                                   products: List[Atoms],
                                   catalyst: Optional[str]) -> Optional[float]:
        """
        Estimate activation barrier using empirical methods.
        
        This is a simplified approach. In practice, you would use:
        - Transition state calculations
        - Hammond's postulate
        - Linear free energy relationships
        - Machine learning models
        """
        
        # Calculate reaction energy
        try:
            reactant_energies = [self.optimizer.single_point_energy(atoms) for atoms in reactants]
            product_energies = [self.optimizer.single_point_energy(atoms) for atoms in products]
            
            delta_e = sum(product_energies) - sum(reactant_energies)
            
            # Hammond's postulate approximation (more realistic)
            if delta_e > 0:  # Endothermic
                # Transition state closer to products
                barrier = 0.6 * delta_e + 0.8  # Base barrier + fraction of reaction energy
            else:  # Exothermic
                # Transition state closer to reactants - lower barriers for exothermic reactions
                barrier = 0.2 * abs(delta_e) + 0.3  # Lower base barrier for exothermic

            # Catalyst effect (rough estimate)
            if catalyst:
                barrier *= 0.4  # Catalysts reduce barriers by ~60%

            # Ensure reasonable range (more permissive)
            barrier = max(0.05, min(barrier, 3.5))  # 0.05-3.5 eV
            
            return barrier
            
        except Exception as e:
            warnings.warn(f"Could not estimate activation barrier: {e}")
            return None
    
    def _estimate_entropy_change(self, reactants: List[Atoms], 
                               products: List[Atoms]) -> float:
        """
        Estimate entropy change (J/mol/K).
        
        Very rough approximation based on:
        - Number of molecules
        - Molecular complexity
        """
        
        # Count molecules and atoms
        n_reactant_molecules = len(reactants)
        n_product_molecules = len(products)
        
        reactant_atoms = sum(len(atoms) for atoms in reactants)
        product_atoms = sum(len(atoms) for atoms in products)
        
        # Estimate based on molecule count change
        delta_n = n_product_molecules - n_reactant_molecules
        
        # Rough estimates (J/mol/K)
        if delta_n > 0:  # More molecules formed
            delta_s = delta_n * 100  # ~100 J/mol/K per additional molecule
        elif delta_n < 0:  # Fewer molecules formed
            delta_s = delta_n * 120  # Slightly larger penalty for combining
        else:  # Same number of molecules
            # Consider complexity change
            complexity_change = product_atoms - reactant_atoms
            delta_s = complexity_change * 5  # ~5 J/mol/K per atom
        
        return delta_s
    
    def _analyze_solvent_effects(self, reactants: List[Atoms], 
                               products: List[Atoms],
                               solvent: str) -> Dict[str, Any]:
        """Analyze solvent effects on reaction feasibility."""
        
        effects = {
            'solvent': solvent,
            'polarity_effect': 'neutral',
            'solvation_energy_change': 0.0,
            'recommended_solvents': []
        }
        
        if solvent.lower() == 'vacuum':
            effects['polarity_effect'] = 'none'
            effects['recommended_solvents'] = ['water', 'ethanol', 'acetone']
        elif solvent.lower() in ['water', 'h2o']:
            effects['polarity_effect'] = 'high_polar'
            effects['solvation_energy_change'] = -0.1  # Stabilization
        elif solvent.lower() in ['ethanol', 'methanol']:
            effects['polarity_effect'] = 'medium_polar'
            effects['solvation_energy_change'] = -0.05
        else:
            effects['polarity_effect'] = 'unknown'
        
        return effects
    
    def _estimate_temperature_range(self, thermo_result: Dict[str, float]) -> Tuple[float, float]:
        """Estimate favorable temperature range."""
        
        delta_h = thermo_result['delta_h']
        delta_s = thermo_result['delta_s'] / 1000  # Convert to eV/K
        
        if abs(delta_s) < 1e-6:  # Avoid division by zero
            return (200.0, 800.0)  # Default range
        
        # Temperature where ΔG = 0
        equilibrium_temp = delta_h / delta_s
        
        if delta_h > 0 and delta_s > 0:  # Endothermic, entropy-driven
            min_temp = max(equilibrium_temp, 298.15)
            max_temp = min(min_temp + 500, 1000.0)
        elif delta_h < 0 and delta_s < 0:  # Exothermic, enthalpy-driven
            min_temp = 200.0
            max_temp = min(equilibrium_temp, 600.0)
        else:  # Other cases
            min_temp = 250.0
            max_temp = 600.0
        
        return (max(min_temp, 200.0), min(max_temp, 1000.0))
    
    def _assess_pressure_sensitivity(self, reactants: List[Atoms], 
                                   products: List[Atoms]) -> str:
        """Assess pressure sensitivity based on volume change."""
        
        n_reactant_molecules = len(reactants)
        n_product_molecules = len(products)
        
        delta_n = n_product_molecules - n_reactant_molecules
        
        if delta_n > 0:
            return "high_pressure_unfavorable"  # Le Chatelier's principle
        elif delta_n < 0:
            return "high_pressure_favorable"
        else:
            return "pressure_insensitive"
    
    def _generate_recommendations(self, thermo_result: Dict[str, float],
                                kinetic_result: Dict[str, Any],
                                solvent_effects: Dict[str, Any],
                                temperature: float, pressure: float,
                                catalyst: Optional[str]) -> List[str]:
        """Generate recommendations for improving reaction feasibility."""
        
        recommendations = []
        
        # Thermodynamic recommendations
        if thermo_result['delta_g'] > 0:
            recommendations.append("Reaction is thermodynamically unfavorable")
            if thermo_result['delta_h'] > 0:
                recommendations.append("Consider higher temperature (endothermic)")
            if thermo_result['delta_s'] > 0:
                recommendations.append("Higher temperature favors entropy")
        
        # Kinetic recommendations
        if kinetic_result['activation_barrier'] and kinetic_result['activation_barrier'] > 1.5:
            recommendations.append("High activation barrier - consider catalyst")
            if not catalyst:
                recommendations.append("Add catalyst to reduce activation energy")
        
        # Temperature recommendations
        if temperature < 298.15:
            recommendations.append("Consider room temperature or higher")
        elif temperature > 600:
            recommendations.append("High temperature may cause side reactions")
        
        # Solvent recommendations
        if solvent_effects['solvent'] == 'vacuum':
            recommendations.append("Consider using appropriate solvent")
        
        # Pressure recommendations
        pressure_sens = self._assess_pressure_sensitivity([], [])  # Simplified
        if "unfavorable" in pressure_sens:
            recommendations.append("Consider lower pressure")
        elif "favorable" in pressure_sens:
            recommendations.append("Higher pressure may improve yield")
        
        return recommendations
    
    def _calculate_confidence(self, thermo_result: Dict[str, float],
                            kinetic_result: Dict[str, Any],
                            n_reactants: int, n_products: int) -> float:
        """Calculate confidence in feasibility prediction."""
        
        confidence = 0.5  # Base confidence
        
        # Increase confidence for simple reactions
        if n_reactants <= 2 and n_products <= 2:
            confidence += 0.2
        
        # Decrease confidence for complex reactions
        if n_reactants > 3 or n_products > 3:
            confidence -= 0.2
        
        # Adjust based on energy magnitudes
        if abs(thermo_result['delta_g']) > 2.0:  # Strong driving force
            confidence += 0.1
        
        if kinetic_result['activation_barrier'] is not None:
            confidence += 0.1  # We have kinetic estimate
        
        return max(0.1, min(confidence, 0.9))
    
    def _create_infeasible_result(self, reason: str) -> FeasibilityResult:
        """Create a result indicating infeasible reaction."""

        return FeasibilityResult(
            is_feasible=False,
            thermodynamic_feasible=False,
            kinetic_feasible=False,
            delta_h=float('inf'),
            delta_g=float('inf'),
            activation_barrier_estimate=None,
            equilibrium_constant=0.0,
            reaction_rate_estimate=None,
            temperature_range=(298.15, 298.15),
            pressure_sensitivity="unknown",
            solvent_effects={'error': reason},
            recommendations=[f"Error: {reason}"],
            confidence=0.0
        )

    def calculate_thermodynamics_from_energies(self, reactant_energy: float,
                                             product_energy: float,
                                             temperature: float = 298.15) -> Dict[str, float]:
        """
        Calculate thermodynamic properties from energies only.

        Args:
            reactant_energy: Energy of reactants (eV)
            product_energy: Energy of products (eV)
            temperature: Temperature (K)

        Returns:
            Dictionary with thermodynamic properties
        """

        # Calculate energy change
        delta_e = product_energy - reactant_energy

        # Estimate enthalpy change (simplified)
        delta_h = delta_e

        # Estimate entropy change (very rough approximation)
        delta_s = 0.0  # Without molecular information, assume zero

        # Calculate Gibbs energy change
        delta_g = delta_h - temperature * delta_s

        # Calculate equilibrium constant
        equilibrium_constant = np.exp(-delta_g / (self.kb * temperature))

        return {
            'delta_e': delta_e,
            'delta_h': delta_h,
            'delta_s': delta_s,
            'delta_g': delta_g,
            'equilibrium_constant': equilibrium_constant,
            'spontaneous': delta_g < 0
        }


if __name__ == "__main__":
    # Example usage
    from input_handler import InputHandler
    
    handler = InputHandler()
    checker = ReactionFeasibilityChecker()
    
    try:
        # Simple test reaction: H2 + Cl2 -> 2HCl
        h2_coords = "H 0.0 0.0 0.0\nH 0.74 0.0 0.0"
        cl2_coords = "Cl 0.0 0.0 0.0\nCl 1.99 0.0 0.0"
        
        h2 = handler.parse_molecule(h2_coords)
        cl2 = handler.parse_molecule(cl2_coords)
        
        reactants = [h2, cl2]
        products = ["Cl"]  # HCl SMILES
        
        result = checker.check_feasibility(reactants, products)
        
        print(f"Reaction feasible: {result.is_feasible}")
        print(f"ΔG: {result.delta_g:.3f} eV")
        print(f"Activation barrier: {result.activation_barrier_estimate}")
        print(f"Recommendations: {result.recommendations}")
        
    except Exception as e:
        print(f"Test failed: {e}")
