#!/usr/bin/env python3
"""
Full Installation Script for Virtual Chemistry Simulation Lab

This script installs ALL required dependencies for a fully functional
quantum chemistry simulation environment.

Author: AI Chemistry Lab
License: MIT
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Command: {command}")
        print(f"   Error: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python {version.major}.{version.minor} detected")
        print("⚠️  Python 3.8 or higher is required")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True


def install_conda_if_needed():
    """Check if conda is available, suggest installation if not."""
    try:
        subprocess.run(['conda', '--version'], check=True, capture_output=True)
        print("✅ Conda is available")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️  Conda not found. Some packages work better with conda.")
        print("   You can continue with pip, but consider installing Miniconda:")
        print("   https://docs.conda.io/en/latest/miniconda.html")
        return False


def install_core_packages():
    """Install core scientific computing packages."""
    packages = [
        "numpy>=1.24.0",
        "scipy>=1.10.0", 
        "matplotlib>=3.7.0",
        "pandas>=2.0.0",
        "networkx>=3.1.0",
        "click>=8.1.0",
        "pyyaml>=6.0.0",
        "tqdm>=4.65.0",
        "requests>=2.31.0",
        "plotly>=5.15.0"
    ]
    
    command = f"{sys.executable} -m pip install --upgrade " + " ".join(packages)
    return run_command(command, "Installing core packages")


def install_ase():
    """Install ASE (Atomic Simulation Environment)."""
    command = f"{sys.executable} -m pip install ase>=3.22.0"
    return run_command(command, "Installing ASE (Atomic Simulation Environment)")


def install_pyscf():
    """Install PySCF for quantum chemistry calculations."""
    print("🔧 Installing PySCF (this may take a few minutes)...")
    
    # Try pip installation first
    command = f"{sys.executable} -m pip install pyscf>=2.3.0"
    if run_command(command, "Installing PySCF via pip"):
        return True
    
    # If pip fails, try conda
    print("⚠️  Pip installation failed, trying conda...")
    command = "conda install -c conda-forge pyscf -y"
    return run_command(command, "Installing PySCF via conda")


def install_rdkit():
    """Install RDKit for cheminformatics."""
    print("🔧 Installing RDKit...")
    
    # Try conda first (recommended for RDKit)
    if install_conda_if_needed():
        command = "conda install -c conda-forge rdkit -y"
        if run_command(command, "Installing RDKit via conda"):
            return True
    
    # Fallback to pip
    print("⚠️  Conda installation failed or not available, trying pip...")
    command = f"{sys.executable} -m pip install rdkit-pypi"
    return run_command(command, "Installing RDKit via pip")


def install_optional_packages():
    """Install optional but useful packages."""
    packages = [
        "jupyter>=1.0.0",
        "ipywidgets>=8.0.0",
        "py3Dmol>=2.0.0",
        "joblib>=1.3.0"
    ]
    
    command = f"{sys.executable} -m pip install " + " ".join(packages)
    return run_command(command, "Installing optional packages")


def test_installation():
    """Test that all critical packages can be imported."""
    print("\n🧪 Testing installation...")
    
    critical_packages = {
        'numpy': 'NumPy',
        'scipy': 'SciPy', 
        'ase': 'ASE',
        'pyscf': 'PySCF',
        'rdkit': 'RDKit',
        'matplotlib': 'Matplotlib',
        'networkx': 'NetworkX',
        'click': 'Click',
        'yaml': 'PyYAML'
    }
    
    failed = []
    
    for module, name in critical_packages.items():
        try:
            if module == 'yaml':
                import yaml
            elif module == 'rdkit':
                from rdkit import Chem
            else:
                __import__(module)
            print(f"✅ {name}")
        except ImportError as e:
            print(f"❌ {name}: {e}")
            failed.append(name)
    
    if failed:
        print(f"\n❌ Failed to import: {failed}")
        return False
    else:
        print("\n✅ All critical packages imported successfully!")
        return True


def create_test_calculation():
    """Create a simple test to verify quantum chemistry works."""
    print("\n🧪 Testing quantum chemistry calculation...")
    
    test_code = '''
import numpy as np
from ase import Atoms
from ase.calculators.pyscf import PySCF

# Create a simple H2 molecule
atoms = Atoms('H2', positions=[[0, 0, 0], [0, 0, 0.74]])

# Set up PySCF calculator
calc = PySCF(atom=atoms, basis='sto3g', xc='pbe')
atoms.set_calculator(calc)

# Calculate energy
energy = atoms.get_potential_energy()
print(f"H2 energy: {energy:.6f} eV")

# Verify it's a reasonable value (should be negative)
if energy < 0:
    print("✅ Quantum chemistry calculation successful!")
    return True
else:
    print("❌ Unexpected energy value")
    return False
'''
    
    try:
        exec(test_code)
        return True
    except Exception as e:
        print(f"❌ Quantum chemistry test failed: {e}")
        return False


def update_requirements():
    """Update requirements.txt with working versions."""
    requirements = '''# Virtual Chemistry Simulation Lab - Full Requirements
# All packages needed for complete functionality

# Core scientific computing
numpy>=1.24.0
scipy>=1.10.0
matplotlib>=3.7.0
pandas>=2.0.0

# Quantum chemistry and molecular modeling
ase>=3.22.0
pyscf>=2.3.0

# Cheminformatics
rdkit-pypi>=2023.3.1

# Visualization and plotting
plotly>=5.15.0
networkx>=3.1.0

# CLI and utilities
click>=8.1.0
pyyaml>=6.0.0
tqdm>=4.65.0
requests>=2.31.0
joblib>=1.3.0

# Optional: Jupyter notebook support
jupyter>=1.0.0
ipywidgets>=8.0.0
py3Dmol>=2.0.0

# Development and testing
pytest>=7.4.0
black>=23.7.0
'''
    
    with open('requirements_full.txt', 'w') as f:
        f.write(requirements)
    
    print("✅ Created requirements_full.txt")


def main():
    """Main installation function."""
    print("🧪 Virtual Chemistry Simulation Lab - Full Installation")
    print("=" * 60)
    print("This will install ALL required packages for real quantum chemistry calculations.")
    print("Installation may take 10-15 minutes depending on your system.")
    print()
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check for conda
    has_conda = install_conda_if_needed()
    
    print("\n📦 Installing packages...")
    
    # Install packages in order
    steps = [
        ("Core packages", install_core_packages),
        ("ASE", install_ase),
        ("PySCF", install_pyscf),
        ("RDKit", install_rdkit),
        ("Optional packages", install_optional_packages)
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        if not step_func():
            failed_steps.append(step_name)
            print(f"⚠️  {step_name} installation had issues")
    
    # Test installation
    print(f"\n{'='*20} Testing {'='*20}")
    if test_installation():
        print("\n🧪 Running quantum chemistry test...")
        if create_test_calculation():
            print("\n🎉 INSTALLATION SUCCESSFUL!")
            print("Your Virtual Chemistry Simulation Lab is ready for real quantum chemistry!")
        else:
            print("\n⚠️  Installation completed but quantum chemistry test failed")
            print("You may need to troubleshoot PySCF installation")
    else:
        print("\n❌ Installation incomplete. Please check the errors above.")
        if failed_steps:
            print(f"Failed steps: {failed_steps}")
        sys.exit(1)
    
    # Update requirements file
    update_requirements()
    
    print("\n" + "="*60)
    print("🚀 Next Steps:")
    print("1. Test the installation: python3 test_installation.py")
    print("2. Run a sample reaction: python3 examples/sample_reactions.py --reaction h2_dissociation")
    print("3. Try your own reaction: python3 main.py -r 'C' -r 'O=O'")
    print("\n✨ You now have a fully functional quantum chemistry simulation lab!")


if __name__ == "__main__":
    main()
