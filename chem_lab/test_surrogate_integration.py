#!/usr/bin/env python3
"""
Test Surrogate Model Integration

Comprehensive testing of the ML surrogate model integration for:
1. Product prediction
2. Reaction feasibility
3. Yield estimation
4. Activation energy prediction

Tests the H2 + O2 reaction that previously failed with DFT.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import time
import warnings
from input_handler import InputHandler
from product_predictor import ProductPredictor
from reaction_feasibility import ReactionFeasibilityChecker
from surrogate_models import SurrogateModelManager

def test_surrogate_model_manager():
    """Test the surrogate model manager directly."""
    print("🧪 Testing Surrogate Model Manager")
    print("=" * 60)
    
    try:
        manager = SurrogateModelManager()
        
        # Test feature extraction
        features = manager.extract_reaction_features(
            reactants=['[H][H]', 'O=O'],
            temperature=600.0,
            pressure=1.0,
            solvent='vacuum'
        )
        
        print(f"✅ Feature extraction successful")
        print(f"   Reactants: {features.reactant_smiles}")
        print(f"   Temperature: {features.temperature}")
        print(f"   Descriptors: {len(features.molecular_descriptors)} features")
        
        # Test individual predictions
        print("\n🔮 Testing ML Predictions:")
        
        # Feasibility
        feasibility = manager.predict_feasibility(features)
        print(f"   Feasibility: {feasibility.prediction} (confidence: {feasibility.confidence:.3f})")
        
        # Products
        products = manager.predict_products(features)
        print(f"   Products: {products.prediction} (confidence: {products.confidence:.3f})")
        
        # Yield
        yield_pred = manager.predict_yield(features)
        print(f"   Yield: {yield_pred.prediction:.3f} (confidence: {yield_pred.confidence:.3f})")
        
        # Activation energy
        activation = manager.predict_activation_energy(features)
        print(f"   Activation: {activation.prediction:.3f} eV (confidence: {activation.confidence:.3f})")
        
        return True
        
    except Exception as e:
        print(f"❌ Surrogate model manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_product_predictor():
    """Test the enhanced product predictor with ML integration."""
    print("\n\n🧪 Testing Enhanced Product Predictor")
    print("=" * 60)
    
    try:
        predictor = ProductPredictor(use_surrogate_models=True)
        
        # Test H2 + O2 reaction
        reactants = ['[H][H]', 'O=O']
        
        print(f"Reactants: {reactants}")
        
        start_time = time.time()
        result = predictor.predict_products(
            reactants=reactants,
            temperature=600.0,
            pressure=1.0,
            use_llm=False  # Focus on ML models
        )
        prediction_time = time.time() - start_time
        
        print(f"✅ Product prediction successful")
        print(f"   Products: {result.products}")
        print(f"   Confidence: {result.confidence:.3f}")
        print(f"   Method: {result.method}")
        print(f"   Prediction time: {prediction_time:.3f} seconds")
        
        # Test with correct stoichiometry
        reactants_2h2 = ['[H][H]', '[H][H]', 'O=O']
        result_2h2 = predictor.predict_products(
            reactants=reactants_2h2,
            temperature=600.0,
            pressure=1.0,
            use_llm=False
        )
        
        print(f"\n2H2 + O2 prediction:")
        print(f"   Products: {result_2h2.products}")
        print(f"   Confidence: {result_2h2.confidence:.3f}")
        print(f"   Method: {result_2h2.method}")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced product predictor test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_feasibility_checker():
    """Test the enhanced feasibility checker with ML integration."""
    print("\n\n🧪 Testing Enhanced Feasibility Checker")
    print("=" * 60)
    
    try:
        # Initialize components
        handler = InputHandler()
        checker = ReactionFeasibilityChecker(use_surrogate_models=True, fallback_to_dft=False)
        
        # Parse reactants
        h2_atoms = handler.parse_molecule('[H][H]')
        o2_atoms = handler.parse_molecule('O=O')
        reactants = [h2_atoms, o2_atoms]
        
        # Test with ML-predicted products
        products = ['O']  # Water (H2O)
        
        print(f"Reactants: H2 + O2")
        print(f"Products: {products}")
        
        start_time = time.time()
        result = checker.check_feasibility(
            reactants=reactants,
            products=products,
            temperature=600.0,
            pressure=1.0,
            solvent='vacuum'
        )
        feasibility_time = time.time() - start_time
        
        print(f"✅ Feasibility check successful")
        print(f"   Is feasible: {result.is_feasible}")
        print(f"   Thermodynamic feasible: {result.thermodynamic_feasible}")
        print(f"   Kinetic feasible: {result.kinetic_feasible}")
        print(f"   ΔG: {result.delta_g:.3f} eV")
        print(f"   Activation barrier: {result.activation_barrier_estimate:.3f} eV")
        print(f"   Confidence: {result.confidence:.3f}")
        print(f"   Computation method: {result.computation_method}")
        print(f"   Feasibility time: {feasibility_time:.3f} seconds")
        
        if result.ml_predictions:
            print(f"\n📊 ML Prediction Details:")
            for pred_type, pred_result in result.ml_predictions.items():
                print(f"   {pred_type}: {pred_result.prediction} (conf: {pred_result.confidence:.3f})")
        
        print(f"\n💡 Recommendations:")
        for rec in result.recommendations:
            print(f"   - {rec}")
        
        return result.is_feasible
        
    except Exception as e:
        print(f"❌ Enhanced feasibility checker test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_integration():
    """Test the full integration with main.py workflow."""
    print("\n\n🧪 Testing Full Integration")
    print("=" * 60)
    
    try:
        # Import main components
        from main import ChemLab
        
        # Initialize with surrogate models
        config = {
            'temperature': 600.0,
            'pressure': 1.0,
            'dft_method': 'PBE',
            'basis_set': '6-31G*',
            'use_llm': False,  # Focus on ML models
            'visualize_results': False,
            'save_output': False
        }
        
        lab = ChemLab(config=config)
        
        # Override to use surrogate models
        lab.product_predictor = ProductPredictor(use_surrogate_models=True)
        lab.feasibility_checker = ReactionFeasibilityChecker(use_surrogate_models=True, fallback_to_dft=False)
        
        # Test H2 + O2 reaction
        reactants = ['[H][H]', 'O=O']
        
        print(f"Running full simulation for: {reactants}")
        
        start_time = time.time()
        results = lab.run_complete_simulation(reactants)
        total_time = time.time() - start_time
        
        print(f"✅ Full simulation completed")
        print(f"   Total time: {total_time:.3f} seconds")
        print(f"   Feasible: {results['feasibility'].is_feasible}")
        print(f"   Products: {results['predicted_products'].products}")
        print(f"   Method: {results['predicted_products'].method}")
        print(f"   Computation: {results['feasibility'].computation_method}")
        
        return results['feasibility'].is_feasible
        
    except Exception as e:
        print(f"❌ Full integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def benchmark_performance():
    """Benchmark ML vs DFT performance."""
    print("\n\n⚡ Performance Benchmark")
    print("=" * 60)
    
    try:
        handler = InputHandler()
        h2_atoms = handler.parse_molecule('[H][H]')
        o2_atoms = handler.parse_molecule('O=O')
        reactants = [h2_atoms, o2_atoms]
        products = ['O']
        
        # Test ML approach
        print("🤖 ML Approach:")
        ml_checker = ReactionFeasibilityChecker(use_surrogate_models=True, fallback_to_dft=False)
        
        start_time = time.time()
        ml_result = ml_checker.check_feasibility(reactants, products, 600.0)
        ml_time = time.time() - start_time
        
        print(f"   Time: {ml_time:.3f} seconds")
        print(f"   Feasible: {ml_result.is_feasible}")
        print(f"   Confidence: {ml_result.confidence:.3f}")
        
        # Test DFT approach (if available)
        print("\n🔬 DFT Approach:")
        try:
            dft_checker = ReactionFeasibilityChecker(use_surrogate_models=False)
            
            start_time = time.time()
            # Use a timeout to avoid hanging
            import signal
            
            def timeout_handler(signum, frame):
                raise TimeoutError("DFT calculation timed out")
            
            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(30)  # 30 second timeout
            
            try:
                dft_result = dft_checker.check_feasibility(reactants, products, 600.0)
                dft_time = time.time() - start_time
                signal.alarm(0)  # Cancel timeout
                
                print(f"   Time: {dft_time:.3f} seconds")
                print(f"   Feasible: {dft_result.is_feasible}")
                print(f"   Confidence: {dft_result.confidence:.3f}")
                
                print(f"\n📈 Performance Improvement:")
                print(f"   Speedup: {dft_time/ml_time:.1f}x faster with ML")
                
            except TimeoutError:
                print("   DFT calculation timed out (>30s)")
                print(f"   ML is >100x faster")
                
        except Exception as e:
            print(f"   DFT test failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance benchmark failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Surrogate Model Integration Test Suite")
    print("=" * 80)
    
    tests = [
        ("Surrogate Model Manager", test_surrogate_model_manager),
        ("Enhanced Product Predictor", test_enhanced_product_predictor),
        ("Enhanced Feasibility Checker", test_enhanced_feasibility_checker),
        ("Full Integration", test_full_integration),
        ("Performance Benchmark", benchmark_performance)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*80}")
    print("📊 Test Summary")
    print("=" * 80)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Surrogate model integration successful.")
        print("\n🔬 H2 + O2 reaction should now work correctly!")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()
