"""
Network Model Module

Builds and analyzes reaction network graphs with molecules as nodes
and reactions as edges. Includes pathway analysis and network properties.

Author: AI Chemistry Lab
License: MIT
"""

import numpy as np
import warnings
from typing import List, Dict, Any, Optional, Tuple, Set
from dataclasses import dataclass
from ase import Atoms

try:
    import networkx as nx
    NETWORKX_AVAILABLE = True
except ImportError:
    NETWORKX_AVAILABLE = False
    warnings.warn("NetworkX not available. Network analysis will be limited.")

from reaction_pathway import PathwayResult


@dataclass
class NetworkNode:
    """Represents a molecule node in the reaction network."""
    id: str
    smiles: str
    atoms: Optional[Atoms]
    energy: float
    properties: Dict[str, Any]


@dataclass
class NetworkEdge:
    """Represents a reaction edge in the reaction network."""
    source: str
    target: str
    activation_energy: float
    reaction_energy: float
    rate_constant: float
    pathway: Optional[PathwayResult]
    properties: Dict[str, Any]


@dataclass
class ReactionNetwork:
    """Container for complete reaction network."""
    nodes: Dict[str, NetworkNode]
    edges: List[NetworkEdge]
    graph: Optional[Any]  # NetworkX graph
    properties: Dict[str, Any]


class ReactionNetworkModel:
    """
    Builds and analyzes reaction networks.
    
    Features:
    - Network construction from pathways
    - Shortest path analysis
    - Centrality measures
    - Subnetwork identification
    - Kinetic network analysis
    """
    
    def __init__(self):
        """Initialize the network model."""
        self.networks = {}  # Store multiple networks
        self.node_counter = 0
        
        # Network analysis parameters
        self.energy_threshold = 2.0  # eV - max barrier for viable pathways
        self.rate_threshold = 1e-10  # s⁻¹ - min rate for viable pathways
    
    def build_network(self, reactants: List[Atoms], 
                     products: List[str],
                     pathway: PathwayResult,
                     network_id: str = "default") -> ReactionNetwork:
        """
        Build reaction network from pathway calculation.
        
        Args:
            reactants: List of reactant Atoms objects
            products: List of product SMILES strings
            pathway: Calculated reaction pathway
            network_id: Identifier for this network
            
        Returns:
            ReactionNetwork object
        """
        
        print(f"🕸️ Building reaction network: {network_id}")
        
        # Initialize network components
        nodes = {}
        edges = []
        
        # Create nodes for reactants and products
        reactant_node = self._create_node_from_atoms(reactants, "reactants")
        nodes[reactant_node.id] = reactant_node
        
        product_node = self._create_node_from_products(products, "products")
        nodes[product_node.id] = product_node
        
        # Add intermediate nodes from pathway
        intermediates = self._identify_intermediates(pathway)
        for i, intermediate in enumerate(intermediates):
            node = self._create_node_from_atoms([intermediate], f"intermediate_{i}")
            nodes[node.id] = node
        
        # Create edges for reaction steps
        pathway_edges = self._create_pathway_edges(pathway, nodes)
        edges.extend(pathway_edges)
        
        # Build NetworkX graph if available
        graph = None
        if NETWORKX_AVAILABLE:
            graph = self._build_networkx_graph(nodes, edges)
        
        # Calculate network properties
        properties = self._calculate_network_properties(nodes, edges, graph)
        
        # Create network object
        network = ReactionNetwork(
            nodes=nodes,
            edges=edges,
            graph=graph,
            properties=properties
        )
        
        # Store network
        self.networks[network_id] = network
        
        return network
    
    def _create_node_from_atoms(self, atoms_list: List[Atoms], 
                              node_type: str) -> NetworkNode:
        """Create a network node from Atoms objects."""
        
        # Generate unique ID
        node_id = f"{node_type}_{self.node_counter}"
        self.node_counter += 1
        
        # Combine multiple molecules if needed
        if len(atoms_list) == 1:
            atoms = atoms_list[0]
            smiles = self._atoms_to_smiles(atoms)
        else:
            # Multiple molecules - create combined representation
            atoms = self._combine_atoms(atoms_list)
            smiles = ".".join([self._atoms_to_smiles(a) for a in atoms_list])
        
        # Calculate energy
        try:
            energy = atoms.get_potential_energy()
        except:
            energy = 0.0
        
        # Calculate properties
        properties = {
            'n_atoms': len(atoms),
            'formula': atoms.get_chemical_formula(),
            'n_molecules': len(atoms_list),
            'node_type': node_type
        }
        
        return NetworkNode(
            id=node_id,
            smiles=smiles,
            atoms=atoms,
            energy=energy,
            properties=properties
        )
    
    def _create_node_from_products(self, products: List[str], 
                                 node_type: str) -> NetworkNode:
        """Create a network node from product SMILES."""
        
        # Generate unique ID
        node_id = f"{node_type}_{self.node_counter}"
        self.node_counter += 1
        
        # Combine SMILES
        smiles = ".".join(products)
        
        # Convert to atoms if possible
        atoms = None
        energy = 0.0
        try:
            from input_handler import InputHandler
            handler = InputHandler()
            
            if len(products) == 1:
                atoms = handler.parse_molecule(products[0])
                energy = atoms.get_potential_energy()
            else:
                # Multiple products
                atoms_list = [handler.parse_molecule(p) for p in products]
                atoms = self._combine_atoms(atoms_list)
                energy = sum(a.get_potential_energy() for a in atoms_list)
        except:
            pass
        
        # Calculate properties
        properties = {
            'n_molecules': len(products),
            'node_type': node_type,
            'smiles_list': products
        }
        
        if atoms:
            properties.update({
                'n_atoms': len(atoms),
                'formula': atoms.get_chemical_formula()
            })
        
        return NetworkNode(
            id=node_id,
            smiles=smiles,
            atoms=atoms,
            energy=energy,
            properties=properties
        )
    
    def _identify_intermediates(self, pathway: PathwayResult) -> List[Atoms]:
        """Identify intermediate structures from pathway."""
        
        intermediates = []
        
        # Look for local minima in energy profile
        energies = pathway.energies
        
        for i in range(1, len(energies) - 1):
            # Check if this is a local minimum
            if energies[i] < energies[i-1] and energies[i] < energies[i+1]:
                # Additional check: energy difference threshold
                if (energies[i-1] - energies[i] > 0.1 and 
                    energies[i+1] - energies[i] > 0.1):
                    intermediates.append(pathway.images[i])
        
        return intermediates
    
    def _create_pathway_edges(self, pathway: PathwayResult, 
                            nodes: Dict[str, NetworkNode]) -> List[NetworkEdge]:
        """Create edges representing reaction steps."""
        
        edges = []
        
        # Get node IDs in order
        node_ids = list(nodes.keys())
        
        # Create edges between consecutive nodes
        for i in range(len(node_ids) - 1):
            source_id = node_ids[i]
            target_id = node_ids[i + 1]
            
            source_node = nodes[source_id]
            target_node = nodes[target_id]
            
            # Calculate edge properties
            reaction_energy = target_node.energy - source_node.energy
            
            # Estimate activation energy (simplified)
            activation_energy = max(0.5, abs(reaction_energy) * 0.7)
            
            # Estimate rate constant (simplified)
            kb = 8.617e-5  # eV/K
            temperature = 298.15  # K
            rate_constant = 1e13 * np.exp(-activation_energy / (kb * temperature))
            
            # Create edge
            edge = NetworkEdge(
                source=source_id,
                target=target_id,
                activation_energy=activation_energy,
                reaction_energy=reaction_energy,
                rate_constant=rate_constant,
                pathway=pathway,
                properties={
                    'step_type': 'elementary',
                    'reversible': True
                }
            )
            
            edges.append(edge)
        
        return edges
    
    def _build_networkx_graph(self, nodes: Dict[str, NetworkNode], 
                            edges: List[NetworkEdge]) -> Any:
        """Build NetworkX graph from nodes and edges."""
        
        if not NETWORKX_AVAILABLE:
            return None
        
        # Create directed graph
        G = nx.DiGraph()
        
        # Add nodes
        for node_id, node in nodes.items():
            G.add_node(node_id, 
                      smiles=node.smiles,
                      energy=node.energy,
                      **node.properties)
        
        # Add edges
        for edge in edges:
            G.add_edge(edge.source, edge.target,
                      activation_energy=edge.activation_energy,
                      reaction_energy=edge.reaction_energy,
                      rate_constant=edge.rate_constant,
                      **edge.properties)
        
        return G
    
    def _calculate_network_properties(self, nodes: Dict[str, NetworkNode],
                                    edges: List[NetworkEdge],
                                    graph: Optional[Any]) -> Dict[str, Any]:
        """Calculate network-level properties."""
        
        properties = {
            'n_nodes': len(nodes),
            'n_edges': len(edges),
            'node_types': list(set(n.properties.get('node_type', 'unknown') 
                                 for n in nodes.values())),
            'energy_span': self._calculate_energy_span(nodes),
            'max_barrier': max((e.activation_energy for e in edges), default=0.0),
            'min_rate': min((e.rate_constant for e in edges), default=0.0)
        }
        
        # NetworkX-specific properties
        if graph and NETWORKX_AVAILABLE:
            try:
                properties.update({
                    'is_connected': nx.is_weakly_connected(graph),
                    'n_components': nx.number_weakly_connected_components(graph),
                    'diameter': nx.diameter(graph) if nx.is_weakly_connected(graph) else None,
                    'density': nx.density(graph),
                    'average_clustering': nx.average_clustering(graph.to_undirected())
                })
            except:
                pass
        
        return properties
    
    def _calculate_energy_span(self, nodes: Dict[str, NetworkNode]) -> float:
        """Calculate energy span of the network."""
        
        energies = [node.energy for node in nodes.values() if node.energy != 0.0]
        
        if not energies:
            return 0.0
        
        return max(energies) - min(energies)
    
    def analyze_pathways(self, network: ReactionNetwork,
                        source: str, target: str) -> Dict[str, Any]:
        """Analyze pathways between two nodes."""
        
        if not network.graph or not NETWORKX_AVAILABLE:
            return {'error': 'NetworkX not available'}
        
        try:
            # Find shortest path
            shortest_path = nx.shortest_path(network.graph, source, target)
            
            # Find all simple paths
            all_paths = list(nx.all_simple_paths(
                network.graph, source, target, cutoff=5
            ))
            
            # Calculate path properties
            path_analysis = []
            for path in all_paths[:10]:  # Limit to first 10 paths
                path_energy = self._calculate_path_energy(network, path)
                path_rate = self._calculate_path_rate(network, path)
                
                path_analysis.append({
                    'path': path,
                    'length': len(path) - 1,
                    'total_barrier': path_energy['total_barrier'],
                    'rate_limiting_step': path_energy['rate_limiting_step'],
                    'overall_rate': path_rate
                })
            
            return {
                'shortest_path': shortest_path,
                'n_paths': len(all_paths),
                'path_analysis': path_analysis
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def _calculate_path_energy(self, network: ReactionNetwork, 
                             path: List[str]) -> Dict[str, Any]:
        """Calculate energy properties of a pathway."""
        
        total_barrier = 0.0
        max_barrier = 0.0
        rate_limiting_step = None
        
        for i in range(len(path) - 1):
            source = path[i]
            target = path[i + 1]
            
            # Find edge
            edge = None
            for e in network.edges:
                if e.source == source and e.target == target:
                    edge = e
                    break
            
            if edge:
                total_barrier += edge.activation_energy
                if edge.activation_energy > max_barrier:
                    max_barrier = edge.activation_energy
                    rate_limiting_step = (source, target)
        
        return {
            'total_barrier': total_barrier,
            'max_barrier': max_barrier,
            'rate_limiting_step': rate_limiting_step
        }
    
    def _calculate_path_rate(self, network: ReactionNetwork, 
                           path: List[str]) -> float:
        """Calculate overall rate for a pathway."""
        
        # For a series of elementary steps, the overall rate is limited
        # by the slowest step
        min_rate = float('inf')
        
        for i in range(len(path) - 1):
            source = path[i]
            target = path[i + 1]
            
            # Find edge
            for edge in network.edges:
                if edge.source == source and edge.target == target:
                    if edge.rate_constant < min_rate:
                        min_rate = edge.rate_constant
                    break
        
        return min_rate if min_rate != float('inf') else 0.0
    
    def _atoms_to_smiles(self, atoms: Atoms) -> str:
        """Convert Atoms object to SMILES string."""
        
        try:
            from rdkit import Chem
            from rdkit.Chem import AllChem
            
            # This is a simplified conversion
            # In practice, you'd need a more sophisticated approach
            formula = atoms.get_chemical_formula()
            return formula  # Placeholder
        except:
            return atoms.get_chemical_formula()
    
    def _combine_atoms(self, atoms_list: List[Atoms]) -> Atoms:
        """Combine multiple Atoms objects into one."""
        
        if len(atoms_list) == 1:
            return atoms_list[0].copy()
        
        combined = atoms_list[0].copy()
        for atoms in atoms_list[1:]:
            atoms_copy = atoms.copy()
            # Translate to avoid overlap
            atoms_copy.translate([len(combined) * 3.0, 0, 0])
            combined.extend(atoms_copy)
        
        return combined
    
    def export_network(self, network: ReactionNetwork, 
                      format: str = "json") -> Dict[str, Any]:
        """Export network in various formats."""
        
        if format == "json":
            return {
                'nodes': {
                    node_id: {
                        'smiles': node.smiles,
                        'energy': node.energy,
                        'properties': node.properties
                    }
                    for node_id, node in network.nodes.items()
                },
                'edges': [
                    {
                        'source': edge.source,
                        'target': edge.target,
                        'activation_energy': edge.activation_energy,
                        'reaction_energy': edge.reaction_energy,
                        'rate_constant': edge.rate_constant,
                        'properties': edge.properties
                    }
                    for edge in network.edges
                ],
                'properties': network.properties
            }
        else:
            raise ValueError(f"Unsupported export format: {format}")


if __name__ == "__main__":
    # Example usage
    from input_handler import InputHandler
    from reaction_pathway import ReactionPathwayCalculator
    
    handler = InputHandler()
    pathway_calculator = ReactionPathwayCalculator()
    network_model = ReactionNetworkModel()
    
    try:
        # Simple test
        h2_coords = "H 0.0 0.0 0.0\nH 0.74 0.0 0.0"
        initial = handler.parse_molecule(h2_coords)
        
        # Calculate pathway
        pathway = pathway_calculator.calculate_pathway(
            [initial], ["[H]"], n_images=5, method="NEB"
        )
        
        # Build network
        network = network_model.build_network(
            [initial], ["[H]"], pathway, "test_network"
        )
        
        print(f"Network built: {network.properties['n_nodes']} nodes, {network.properties['n_edges']} edges")
        print(f"Energy span: {network.properties['energy_span']:.3f} eV")
        print(f"Max barrier: {network.properties['max_barrier']:.3f} eV")
        
        # Export network
        exported = network_model.export_network(network)
        print(f"Network exported with {len(exported['nodes'])} nodes")
        
    except Exception as e:
        print(f"Test failed: {e}")
