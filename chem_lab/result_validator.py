"""
Result Validation Module

Implements validation rules and confidence scoring for reaction predictions.
Provides fallback logic when DFT calculations fail or produce unrealistic results.

Author: AI Chemistry Lab
License: MIT
"""

import numpy as np
import warnings
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from reaction_pathway import PathwayR<PERSON>ult
from reaction_feasibility import FeasibilityResult
from product_predictor import PredictionResult
from network_model import ReactionNetwork


class ValidationStatus(Enum):
    """Status of validation checks."""
    PASSED = "passed"
    FAILED = "failed"
    WARNING = "warning"
    DISCARDED = "discarded"


class PredictionSource(Enum):
    """Source of final prediction."""
    DFT_NEB = "dft_neb"
    ML_SURROGATE = "ml_surrogate"
    REACTION_NETWORK = "reaction_network"
    ENSEMBLE = "ensemble"
    FALLBACK = "fallback"


@dataclass
class ValidationResult:
    """Container for validation results."""
    status: ValidationStatus
    message: str
    confidence_score: float
    issues: List[str]
    recommendations: List[str]


@dataclass
class ValidatedPrediction:
    """Container for validated and final prediction results."""
    # Core prediction data
    products: List[str]
    activation_energy: float  # kcal/mol
    reaction_energy: float   # kcal/mol
    delta_g: float          # kcal/mol
    delta_h: float          # kcal/mol
    is_feasible: bool
    
    # Validation metadata
    prediction_source: PredictionSource
    confidence_score: float
    validation_status: ValidationStatus
    model_agreement: Dict[str, bool]
    
    # Detailed results from different methods
    dft_result: Optional[Dict[str, Any]]
    ml_result: Optional[Dict[str, Any]]
    network_result: Optional[Dict[str, Any]]
    
    # Quality metrics
    convergence_status: Dict[str, bool]
    uncertainty_estimates: Dict[str, float]
    validation_messages: List[str]


class ResultValidator:
    """
    Validates reaction prediction results and implements fallback logic.
    
    Key validation rules:
    1. If converged == False, discard NEB result
    2. If activation_energy > 200 kcal/mol, flag as outlier
    3. If ML and Network agree (products + Ea < 5 kcal/mol), mark high-confidence
    4. Use ensemble approach when multiple methods agree
    """
    
    def __init__(self):
        """Initialize the validator with default thresholds."""
        # Validation thresholds
        self.max_activation_energy = 200.0  # kcal/mol
        self.high_confidence_threshold = 0.8
        self.ml_network_agreement_threshold = 5.0  # kcal/mol difference
        self.min_confidence_for_dft = 0.6
        
        # Conversion factors
        self.ev_to_kcal_mol = 23.06  # eV to kcal/mol
    
    def validate_and_select_prediction(self, 
                                     pathway_result: Optional[PathwayResult],
                                     feasibility_result: FeasibilityResult,
                                     product_result: PredictionResult,
                                     network_result: Optional[ReactionNetwork],
                                     thermo_data: Optional[Dict[str, Any]]) -> ValidatedPrediction:
        """
        Validate all prediction results and select the most reliable final prediction.
        
        Args:
            pathway_result: NEB pathway calculation result
            feasibility_result: ML feasibility analysis
            product_result: Product prediction result
            network_result: Reaction network analysis
            thermo_data: Thermodynamic calculation results
            
        Returns:
            ValidatedPrediction with final results and validation metadata
        """
        
        validation_messages = []
        convergence_status = {}
        uncertainty_estimates = {}
        model_agreement = {}
        
        # Step 1: Validate DFT/NEB results
        dft_validation = self._validate_dft_results(pathway_result, thermo_data)
        dft_result = self._extract_dft_data(pathway_result, thermo_data) if dft_validation.status == ValidationStatus.PASSED else None
        
        if dft_validation.status != ValidationStatus.PASSED:
            validation_messages.append(f"DFT validation: {dft_validation.message}")
            convergence_status['dft'] = False
        else:
            convergence_status['dft'] = True
        
        # Step 2: Extract ML results
        ml_result = self._extract_ml_data(feasibility_result, product_result)
        ml_validation = self._validate_ml_results(ml_result)
        validation_messages.append(f"ML validation: {ml_validation.message}")
        convergence_status['ml'] = ml_validation.status == ValidationStatus.PASSED
        
        # Step 3: Extract network results
        network_data = self._extract_network_data(network_result) if network_result else None
        if network_data:
            network_validation = self._validate_network_results(network_data)
            validation_messages.append(f"Network validation: {network_validation.message}")
            convergence_status['network'] = network_validation.status == ValidationStatus.PASSED
        else:
            convergence_status['network'] = False
        
        # Step 4: Check model agreement
        model_agreement = self._assess_model_agreement(dft_result, ml_result, network_data)
        
        # Step 5: Select final prediction using decision rules
        final_prediction = self._select_final_prediction(
            dft_result, ml_result, network_data, model_agreement, validation_messages
        )
        
        return final_prediction

    def _validate_dft_results(self, pathway_result: Optional[PathwayResult],
                             thermo_data: Optional[Dict[str, Any]]) -> ValidationResult:
        """
        Validate DFT/NEB calculation results.

        Validation rules:
        1. Check if calculation converged
        2. Check if activation energy is realistic (< 200 kcal/mol)
        3. Check for numerical stability
        """

        if not pathway_result:
            return ValidationResult(
                status=ValidationStatus.FAILED,
                message="No pathway result available",
                confidence_score=0.0,
                issues=["Missing pathway calculation"],
                recommendations=["Run pathway calculation"]
            )

        issues = []
        recommendations = []
        confidence = 0.5

        # Rule 1: Check convergence
        if not getattr(pathway_result, 'converged', True):
            issues.append("NEB calculation did not converge")
            recommendations.append("Improve initial guess or increase max iterations")
            return ValidationResult(
                status=ValidationStatus.DISCARDED,
                message="NEB calculation failed to converge - discarding result",
                confidence_score=0.0,
                issues=issues,
                recommendations=recommendations
            )

        # Rule 2: Check activation energy magnitude
        activation_energy_ev = getattr(pathway_result, 'activation_energy', None)
        if activation_energy_ev is not None:
            activation_energy_kcal = activation_energy_ev * self.ev_to_kcal_mol

            if activation_energy_kcal > self.max_activation_energy:
                issues.append(f"Activation energy too high: {activation_energy_kcal:.1f} kcal/mol")
                recommendations.append("Check transition state geometry and pathway")
                return ValidationResult(
                    status=ValidationStatus.DISCARDED,
                    message=f"Activation energy ({activation_energy_kcal:.1f} kcal/mol) exceeds threshold",
                    confidence_score=0.0,
                    issues=issues,
                    recommendations=recommendations
                )

            # Adjust confidence based on energy magnitude
            if activation_energy_kcal < 50:
                confidence += 0.2
            elif activation_energy_kcal > 100:
                confidence -= 0.1

        # Rule 3: Check for numerical stability
        if thermo_data:
            rate_constant = thermo_data.get('rate_constant', 0)
            if rate_constant == 0 and activation_energy_ev is not None:
                issues.append("Zero rate constant despite finite activation energy")
                recommendations.append("Check thermodynamic calculation consistency")
                confidence -= 0.2

        # Determine final status
        if issues:
            status = ValidationStatus.WARNING
            message = f"DFT results have {len(issues)} issues but are usable"
        else:
            status = ValidationStatus.PASSED
            message = "DFT results passed validation"
            confidence += 0.2

        return ValidationResult(
            status=status,
            message=message,
            confidence_score=max(0.0, min(1.0, confidence)),
            issues=issues,
            recommendations=recommendations
        )

    def _validate_ml_results(self, ml_result: Dict[str, Any]) -> ValidationResult:
        """Validate ML prediction results."""

        issues = []
        recommendations = []
        confidence = ml_result.get('confidence', 0.5)

        # Check confidence threshold
        if confidence < 0.3:
            issues.append(f"Low ML confidence: {confidence:.2f}")
            recommendations.append("Consider additional training data or features")

        # Check for missing predictions
        required_fields = ['products', 'activation_energy', 'feasible']
        for field in required_fields:
            if field not in ml_result or ml_result[field] is None:
                issues.append(f"Missing ML prediction: {field}")

        # Determine status
        if len(issues) > 2:
            status = ValidationStatus.FAILED
            message = "ML results failed validation"
        elif issues:
            status = ValidationStatus.WARNING
            message = "ML results have minor issues"
        else:
            status = ValidationStatus.PASSED
            message = "ML results passed validation"

        return ValidationResult(
            status=status,
            message=message,
            confidence_score=confidence,
            issues=issues,
            recommendations=recommendations
        )

    def _validate_network_results(self, network_data: Dict[str, Any]) -> ValidationResult:
        """Validate reaction network results."""

        issues = []
        recommendations = []
        confidence = 0.7  # Default confidence for network predictions

        # Check for reasonable activation energy
        activation_energy = network_data.get('activation_energy', None)
        if activation_energy is not None:
            if activation_energy > 100:  # kcal/mol
                issues.append(f"High network activation energy: {activation_energy:.1f} kcal/mol")
            elif activation_energy < 0:
                issues.append("Negative activation energy from network")
            else:
                confidence += 0.1

        # Check for product consistency
        products = network_data.get('products', [])
        if not products:
            issues.append("No products predicted by network")

        status = ValidationStatus.PASSED if len(issues) == 0 else ValidationStatus.WARNING
        message = f"Network validation: {len(issues)} issues found"

        return ValidationResult(
            status=status,
            message=message,
            confidence_score=confidence,
            issues=issues,
            recommendations=recommendations
        )

    def _extract_dft_data(self, pathway_result: PathwayResult,
                         thermo_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract relevant data from DFT calculations."""

        data = {
            'source': 'DFT',
            'converged': getattr(pathway_result, 'converged', False),
            'activation_energy': getattr(pathway_result, 'activation_energy', None),
            'reaction_energy': getattr(pathway_result, 'reaction_energy', None),
        }

        if data['activation_energy'] is not None:
            data['activation_energy_kcal'] = data['activation_energy'] * self.ev_to_kcal_mol

        if data['reaction_energy'] is not None:
            data['reaction_energy_kcal'] = data['reaction_energy'] * self.ev_to_kcal_mol

        if thermo_data:
            data.update({
                'delta_g': thermo_data.get('delta_g', None),
                'delta_h': thermo_data.get('delta_h', None),
                'rate_constant': thermo_data.get('rate_constant', None),
                'equilibrium_constant': thermo_data.get('equilibrium_constant', None)
            })

        return data

    def _extract_ml_data(self, feasibility_result: FeasibilityResult,
                        product_result: PredictionResult) -> Dict[str, Any]:
        """Extract relevant data from ML predictions."""

        # Convert eV to kcal/mol for activation energy
        activation_energy_ev = feasibility_result.activation_barrier_estimate
        activation_energy_kcal = None
        if activation_energy_ev is not None:
            activation_energy_kcal = activation_energy_ev * self.ev_to_kcal_mol

        data = {
            'source': 'ML',
            'products': product_result.products,
            'confidence': feasibility_result.confidence,
            'feasible': feasibility_result.is_feasible,
            'activation_energy': activation_energy_ev,
            'activation_energy_kcal': activation_energy_kcal,
            'delta_g': feasibility_result.delta_g * self.ev_to_kcal_mol if feasibility_result.delta_g else None,
            'delta_h': feasibility_result.delta_h * self.ev_to_kcal_mol if feasibility_result.delta_h else None,
            'method': feasibility_result.computation_method,
            'ml_predictions': feasibility_result.ml_predictions
        }

        return data

    def _extract_network_data(self, network_result: ReactionNetwork) -> Dict[str, Any]:
        """Extract relevant data from reaction network."""

        # Get activation energy from network edges
        activation_energies = []
        for edge in network_result.edges:
            if edge.activation_energy is not None:
                activation_energies.append(edge.activation_energy)

        min_activation = min(activation_energies) if activation_energies else None

        # Extract products from network nodes
        products = []
        for node_id, node in network_result.nodes.items():
            if node.smiles and node_id != 'reactants':  # Exclude reactant nodes
                products.append(node.smiles)

        data = {
            'source': 'Network',
            'products': products,
            'activation_energy': min_activation,
            'n_pathways': len(network_result.edges),
            'network_properties': network_result.properties
        }

        return data

    def _assess_model_agreement(self, dft_result: Optional[Dict[str, Any]],
                               ml_result: Dict[str, Any],
                               network_data: Optional[Dict[str, Any]]) -> Dict[str, bool]:
        """
        Assess agreement between different prediction methods.

        Returns:
            Dictionary with agreement flags for different comparisons
        """

        agreement = {
            'ml_network_products': False,
            'ml_network_energy': False,
            'dft_ml_energy': False,
            'all_methods_feasible': False,
            'high_confidence_consensus': False
        }

        # Check ML-Network product agreement
        if network_data and ml_result.get('products'):
            ml_products = set(ml_result['products'])
            network_products = set(network_data.get('products', []))
            if ml_products.intersection(network_products):
                agreement['ml_network_products'] = True

        # Check ML-Network energy agreement (within 5 kcal/mol)
        if (network_data and
            ml_result.get('activation_energy_kcal') is not None and
            network_data.get('activation_energy') is not None):

            energy_diff = abs(ml_result['activation_energy_kcal'] - network_data['activation_energy'])
            if energy_diff < self.ml_network_agreement_threshold:
                agreement['ml_network_energy'] = True

        # Check DFT-ML energy agreement
        if (dft_result and
            dft_result.get('activation_energy_kcal') is not None and
            ml_result.get('activation_energy_kcal') is not None):

            energy_diff = abs(dft_result['activation_energy_kcal'] - ml_result['activation_energy_kcal'])
            if energy_diff < 20.0:  # More lenient threshold for DFT-ML comparison
                agreement['dft_ml_energy'] = True

        # Check if all methods predict feasible reaction
        feasible_predictions = []
        if ml_result.get('feasible') is not None:
            feasible_predictions.append(ml_result['feasible'])
        if dft_result and dft_result.get('activation_energy_kcal'):
            feasible_predictions.append(dft_result['activation_energy_kcal'] < 50)
        if network_data and network_data.get('activation_energy'):
            feasible_predictions.append(network_data['activation_energy'] < 50)

        if feasible_predictions and all(feasible_predictions):
            agreement['all_methods_feasible'] = True

        # High confidence consensus: ML + Network agree on products and low energy
        if (agreement['ml_network_products'] and
            agreement['ml_network_energy'] and
            ml_result.get('confidence', 0) > self.high_confidence_threshold):
            agreement['high_confidence_consensus'] = True

        return agreement

    def _select_final_prediction(self, dft_result: Optional[Dict[str, Any]],
                                ml_result: Dict[str, Any],
                                network_data: Optional[Dict[str, Any]],
                                model_agreement: Dict[str, bool],
                                validation_messages: List[str]) -> ValidatedPrediction:
        """
        Select final prediction based on validation rules and model agreement.

        Decision hierarchy:
        1. If ML + Network agree (high confidence) -> Use ensemble
        2. If DFT passed validation and agrees with ML -> Use DFT
        3. If DFT failed but ML + Network available -> Use ML/Network
        4. Fallback to best available method
        """

        # Rule 1: High confidence consensus (ML + Network agree)
        if model_agreement.get('high_confidence_consensus', False):
            return self._create_ensemble_prediction(ml_result, network_data, validation_messages)

        # Rule 2: DFT available and reliable
        if (dft_result and
            dft_result.get('converged', False) and
            dft_result.get('activation_energy_kcal', float('inf')) < self.max_activation_energy):

            # If DFT agrees with ML, use DFT with high confidence
            if model_agreement.get('dft_ml_energy', False):
                return self._create_dft_prediction(dft_result, ml_result, validation_messages, confidence_boost=0.2)
            else:
                return self._create_dft_prediction(dft_result, ml_result, validation_messages)

        # Rule 3: DFT failed, use ML/Network fallback
        if network_data and model_agreement.get('ml_network_products', False):
            validation_messages.append("Using ML/Network fallback due to DFT failure")
            return self._create_fallback_prediction(ml_result, network_data, validation_messages)

        # Rule 4: Final fallback to ML only
        validation_messages.append("Using ML-only prediction as final fallback")
        return self._create_ml_prediction(ml_result, validation_messages)

    def _create_ensemble_prediction(self, ml_result: Dict[str, Any],
                                   network_data: Dict[str, Any],
                                   validation_messages: List[str]) -> ValidatedPrediction:
        """Create prediction from ensemble of ML and network results."""

        # Average activation energies
        ml_ea = ml_result.get('activation_energy_kcal', 0)
        net_ea = network_data.get('activation_energy', 0)
        avg_activation = (ml_ea + net_ea) / 2 if ml_ea and net_ea else (ml_ea or net_ea or 0)

        # Use ML thermodynamics
        delta_g = ml_result.get('delta_g', 0)
        delta_h = ml_result.get('delta_h', 0)

        # Combine products (prefer ML products)
        products = ml_result.get('products', network_data.get('products', []))

        return ValidatedPrediction(
            products=products,
            activation_energy=avg_activation,
            reaction_energy=delta_h,
            delta_g=delta_g,
            delta_h=delta_h,
            is_feasible=ml_result.get('feasible', True),
            prediction_source=PredictionSource.ENSEMBLE,
            confidence_score=min(0.95, ml_result.get('confidence', 0.8) + 0.1),
            validation_status=ValidationStatus.PASSED,
            model_agreement={'ml_network': True},
            dft_result=None,
            ml_result=ml_result,
            network_result=network_data,
            convergence_status={'ml': True, 'network': True, 'dft': False},
            uncertainty_estimates={'activation_energy': abs(ml_ea - net_ea) if ml_ea and net_ea else 0},
            validation_messages=validation_messages + ["High-confidence ensemble prediction"]
        )

    def _create_dft_prediction(self, dft_result: Dict[str, Any],
                              ml_result: Dict[str, Any],
                              validation_messages: List[str],
                              confidence_boost: float = 0.0) -> ValidatedPrediction:
        """Create prediction primarily from DFT results."""

        return ValidatedPrediction(
            products=ml_result.get('products', []),  # Use ML for products
            activation_energy=dft_result.get('activation_energy_kcal', 0),
            reaction_energy=dft_result.get('reaction_energy_kcal', 0),
            delta_g=dft_result.get('delta_g', ml_result.get('delta_g', 0)),
            delta_h=dft_result.get('delta_h', ml_result.get('delta_h', 0)),
            is_feasible=dft_result.get('activation_energy_kcal', float('inf')) < 50,
            prediction_source=PredictionSource.DFT_NEB,
            confidence_score=min(0.9, 0.7 + confidence_boost),
            validation_status=ValidationStatus.PASSED,
            model_agreement={'dft_available': True},
            dft_result=dft_result,
            ml_result=ml_result,
            network_result=None,
            convergence_status={'dft': True, 'ml': True, 'network': False},
            uncertainty_estimates={'method_uncertainty': 0.1},
            validation_messages=validation_messages + ["DFT-based prediction"]
        )

    def _create_fallback_prediction(self, ml_result: Dict[str, Any],
                                   network_data: Dict[str, Any],
                                   validation_messages: List[str]) -> ValidatedPrediction:
        """Create fallback prediction when DFT fails."""

        # Prefer network activation energy if available and reasonable
        activation_energy = network_data.get('activation_energy', ml_result.get('activation_energy_kcal', 0))
        if activation_energy > 50:  # If network gives high energy, use ML
            activation_energy = ml_result.get('activation_energy_kcal', activation_energy)

        return ValidatedPrediction(
            products=ml_result.get('products', []),
            activation_energy=activation_energy,
            reaction_energy=ml_result.get('delta_h', 0),
            delta_g=ml_result.get('delta_g', 0),
            delta_h=ml_result.get('delta_h', 0),
            is_feasible=ml_result.get('feasible', True),
            prediction_source=PredictionSource.FALLBACK,
            confidence_score=ml_result.get('confidence', 0.6),
            validation_status=ValidationStatus.WARNING,
            model_agreement={'ml_network_fallback': True},
            dft_result=None,
            ml_result=ml_result,
            network_result=network_data,
            convergence_status={'dft': False, 'ml': True, 'network': True},
            uncertainty_estimates={'dft_failure_penalty': 0.2},
            validation_messages=validation_messages + ["Fallback prediction due to DFT failure"]
        )

    def _create_ml_prediction(self, ml_result: Dict[str, Any],
                             validation_messages: List[str]) -> ValidatedPrediction:
        """Create prediction from ML results only."""

        return ValidatedPrediction(
            products=ml_result.get('products', []),
            activation_energy=ml_result.get('activation_energy_kcal', 0),
            reaction_energy=ml_result.get('delta_h', 0),
            delta_g=ml_result.get('delta_g', 0),
            delta_h=ml_result.get('delta_h', 0),
            is_feasible=ml_result.get('feasible', True),
            prediction_source=PredictionSource.ML_SURROGATE,
            confidence_score=ml_result.get('confidence', 0.5),
            validation_status=ValidationStatus.WARNING,
            model_agreement={'ml_only': True},
            dft_result=None,
            ml_result=ml_result,
            network_result=None,
            convergence_status={'dft': False, 'ml': True, 'network': False},
            uncertainty_estimates={'single_method_penalty': 0.3},
            validation_messages=validation_messages + ["ML-only prediction (limited validation)"]
        )
