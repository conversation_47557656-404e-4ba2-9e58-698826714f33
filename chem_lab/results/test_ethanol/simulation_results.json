{"predicted_products": "PredictionResult(products=['CCO'], confidence=0.1, method='Heuristics', reasoning='Basic heuristic prediction', alternative_products=None, reaction_type='no_reaction')", "feasibility": "FeasibilityResult(is_feasible=np.True_, thermodynamic_feasible=np.True_, kinetic_feasible=np.True_, delta_h=np.float64(-0.14762840388812037), delta_g=np.float64(-0.14762840388812037), activation_barrier_estimate=np.float64(0.32952568077762406), equilibrium_constant=np.float64(312.9918779336066), reaction_rate_estimate=np.float64(26893517.2939584), temperature_range=(200.0, 800.0), pressure_sensitivity='pressure_insensitive', solvent_effects={'solvent': 'vacuum', 'polarity_effect': 'none', 'solvation_energy_change': 0.0, 'recommended_solvents': ['water', 'ethanol', 'acetone']}, recommendations=['Consider using appropriate solvent'], confidence=0.7999999999999999)", "reaction_pathway": "PathwayResult(images=[Atoms(symbols='C2OH6', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6', pbc=False, calculator=PySCF(...))], energies=array([-4212.95104692, -4212.98928098, -4213.02204701, -4213.04919033,\n       -4213.07056225, -4213.08602124, -4213.09543383, -4213.09867532]), forces=[array([[ 0.84715679, -0.26170281,  0.16756818],\n       [-0.21631132,  0.17316804,  0.39536186],\n       [-1.26871899,  1.29934813, -2.08943675],\n       [-0.04397717, -0.06065697,  0.01076327],\n       [ 0.10666438,  0.07803628,  0.02408177],\n       [ 0.06091929, -0.03061112, -0.11932942],\n       [ 0.07498725,  0.14991716,  0.43032661],\n       [ 0.09168412,  0.34069338, -0.16501523],\n       [ 0.34794976, -1.69002138,  1.34595382]]), array([[ 0.74139925, -0.226836  ,  0.14561425],\n       [-0.18620735,  0.16475756,  0.34660067],\n       [-1.09019939,  1.14363035, -1.81651739],\n       [-0.03847439, -0.05342755,  0.0086272 ],\n       [ 0.09553013,  0.06478406,  0.0220961 ],\n       [ 0.0512893 , -0.02685719, -0.10322999],\n       [ 0.06334467,  0.13070435,  0.37178677],\n       [ 0.07735882,  0.29674909, -0.14454203],\n       [ 0.28641293, -1.49508903,  1.16994885]]), array([[ 0.6311397 , -0.19107921,  0.12321025],\n       [-0.15656963,  0.15280228,  0.29522213],\n       [-0.90987643,  0.97699609, -1.53426743],\n       [-0.03290443, -0.04625833,  0.00650608],\n       [ 0.0845055 ,  0.05173286,  0.02031466],\n       [ 0.0417075 , -0.02306781, -0.08716545],\n       [ 0.05174543,  0.11100871,  0.31288194],\n       [ 0.06314244,  0.25273448, -0.12395387],\n       [ 0.22766819, -1.28620944,  0.9877496 ]]), array([[ 0.51625672, -0.15439624,  0.10035916],\n       [-0.12739764,  0.13708079,  0.24119501],\n       [-0.72817099,  0.7993019 , -1.24293187],\n       [-0.0272676 , -0.03915126,  0.0043992 ],\n       [ 0.07359385,  0.03887825,  0.01873847],\n       [ 0.0321731 , -0.0192432 , -0.07113834],\n       [ 0.04019429,  0.09082712,  0.25361993],\n       [ 0.04902388,  0.20864157, -0.10326029],\n       [ 0.17226448, -1.06306197,  0.79963484]]), array([[ 0.39662759, -0.11675023,  0.07706432],\n       [-0.09868694,  0.11736274,  0.18449079],\n       [-0.54554726,  0.61047822, -0.94282968],\n       [-0.0215646 , -0.03210816,  0.00230589],\n       [ 0.06279801,  0.02621558,  0.01736848],\n       [ 0.02268479, -0.01538343, -0.05515104],\n       [ 0.02869576,  0.0701568 ,  0.19400893],\n       [ 0.03499138,  0.16446133, -0.08247081],\n       [ 0.1207788 , -0.82537305,  0.60593598]]), array([[ 2.72126966e-01, -7.81031049e-02,  5.33296712e-02],\n       [-7.04302568e-02,  9.34082450e-02,  1.25083110e-01],\n       [-3.62501030e-01,  4.10536105e-01, -6.34343846e-01],\n       [-1.57962762e-02, -2.51308696e-02,  2.25441828e-04],\n       [ 5.21205923e-02,  1.37399779e-02,  1.62055502e-02],\n       [ 1.32409912e-02, -1.14885107e-02, -3.92059250e-02],\n       [ 1.72543358e-02,  4.89951751e-02,  1.34057431e-01],\n       [ 2.10326922e-02,  1.20183456e-01, -6.15948693e-02],\n       [ 7.38141587e-02, -5.72924375e-01,  4.07040139e-01]]), array([[ 0.14262671, -0.03841547,  0.02915978],\n       [-0.04261703,  0.06496749,  0.06294815],\n       [-0.17954093,  0.19955894, -0.31790154],\n       [-0.0099636 , -0.01822126, -0.00184291],\n       [ 0.04156403,  0.0014463 ,  0.01525048],\n       [ 0.00383992, -0.00755851, -0.02330542],\n       [ 0.00587453,  0.02733986,  0.0737741 ],\n       [ 0.00713507,  0.07579628, -0.04064175],\n       [ 0.03199724, -0.30556132,  0.20339308]]), array([[ 0.00799568,  0.00235342,  0.00455986],\n       [-0.01523284,  0.03178055, -0.00193481],\n       [ 0.00281613, -0.02229405,  0.00602259],\n       [-0.00406765, -0.01138125, -0.00389995],\n       [ 0.03113056, -0.01067082,  0.01450401],\n       [-0.00552043, -0.00359348, -0.00745196],\n       [-0.00543915,  0.00518864,  0.01316787],\n       [-0.00671464,  0.03128675, -0.01962056],\n       [-0.00402465, -0.02320133, -0.00449853]])], converged=False, n_iterations=200, activation_energy=np.float64(0.0), reaction_energy=np.float64(-0.14762840388812037), transition_state_index=np.int64(0), transition_state=Atoms(symbols='C2OH6', pbc=False), reaction_coordinate=array([0.        , 0.00272719, 0.00545438, 0.00818156, 0.01090875,\n       0.01363594, 0.01636313, 0.01909032]), method='NEB', calculation_time=53.7537899017334)", "thermodynamics": "ThermoKineticResult(temperature=298.15, pressure=1.0, delta_h=np.float64(-0.14762840388812037), delta_g=np.float64(-1.0365626288881202), delta_s=0.0029815, activation_energy=np.float64(0.0), activation_enthalpy=np.float64(0.0), activation_entropy=0.0029815, activation_free_energy=np.float64(-0.888934225), rate_constant=np.float64(6.597216353378955e+27), equilibrium_constant=np.float64(3.323037305301515e+17), half_life=np.float64(1.0504430397300217e-28), arrhenius_parameters={'A': 10000000000000.0, 'Ea': np.float64(0.0), 'k_ref': np.float64(10000000000000.0)}, eyring_parameters={'delta_H_double_dagger': np.float64(0.0), 'delta_S_double_dagger': 0.0029815}, temperature_dependence={'temperatures': [198.14999999999998, 218.14999999999998, 238.14999999999998, 258.15, 278.15, 298.15, 318.15, 338.15, 358.15, 378.15, 398.15], 'rate_constants': [np.float64(4.000753597368431e+22), np.float64(4.485992902737767e+23), np.float64(4.987806395633994e+24), np.float64(5.506641055477155e+25), np.float64(6.042954723855029e+26), np.float64(6.597216353378955e+27), np.float64(7.169906262028387e+28), np.float64(7.761516393102539e+29), np.float64(8.372550580898865e+30), np.float64(9.003524822241591e+31), np.float64(9.65496755398594e+32)], 'equilibrium_constants': [np.float64(55096556755505.87), np.float64(254003399257338.22), np.float64(1337746168218890.2), np.float64(7803483678070909.0), np.float64(4.931858120051425e+16), np.float64(3.323037305301515e+17), np.float64(2.358409168699031e+18), np.float64(1.746865852425558e+19), np.float64(1.3407496738171529e+20), np.float64(1.0603092208956817e+21), np.float64(8.601129919886316e+21)], 'delta_g_values': [np.float64(-0.5402626288881203), np.float64(-0.6235226288881203), np.float64(-0.7147826288881203), np.float64(-0.8140426288881203), np.float64(-0.9213026288881203), np.float64(-1.0365626288881202), np.float64(-1.1598226288881202), np.float64(-1.2910826288881203), np.float64(-1.4303426288881202), np.float64(-1.5776026288881204), np.float64(-1.7328626288881201)]})", "reaction_network": "ReactionNetwork(nodes={'reactants_0': NetworkNode(id='reactants_0', smiles='C2H6O', atoms=Atoms(symbols='C2OH6', pbc=False, calculator=PySCF(...)), energy=np.float64(-4212.951046918896), properties={'n_atoms': 9, 'formula': 'C2H6O', 'n_molecules': 1, 'node_type': 'reactants'}), 'products_1': NetworkNode(id='products_1', smiles='CCO', atoms=Atoms(symbols='C2OH6', pbc=False), energy=0.0, properties={'n_molecules': 1, 'node_type': 'products', 'smiles_list': ['CCO'], 'n_atoms': 9, 'formula': 'C2H6O'})}, edges=[NetworkEdge(source='reactants_0', target='products_1', activation_energy=np.float64(2949.065732843227), reaction_energy=np.float64(4212.951046918896), rate_constant=np.float64(0.0), pathway=PathwayResult(images=[Atoms(symbols='C2OH6', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6', pbc=False, calculator=PySCF(...))], energies=array([-4212.95104692, -4212.98928098, -4213.02204701, -4213.04919033,\n       -4213.07056225, -4213.08602124, -4213.09543383, -4213.09867532]), forces=[array([[ 0.84715679, -0.26170281,  0.16756818],\n       [-0.21631132,  0.17316804,  0.39536186],\n       [-1.26871899,  1.29934813, -2.08943675],\n       [-0.04397717, -0.06065697,  0.01076327],\n       [ 0.10666438,  0.07803628,  0.02408177],\n       [ 0.06091929, -0.03061112, -0.11932942],\n       [ 0.07498725,  0.14991716,  0.43032661],\n       [ 0.09168412,  0.34069338, -0.16501523],\n       [ 0.34794976, -1.69002138,  1.34595382]]), array([[ 0.74139925, -0.226836  ,  0.14561425],\n       [-0.18620735,  0.16475756,  0.34660067],\n       [-1.09019939,  1.14363035, -1.81651739],\n       [-0.03847439, -0.05342755,  0.0086272 ],\n       [ 0.09553013,  0.06478406,  0.0220961 ],\n       [ 0.0512893 , -0.02685719, -0.10322999],\n       [ 0.06334467,  0.13070435,  0.37178677],\n       [ 0.07735882,  0.29674909, -0.14454203],\n       [ 0.28641293, -1.49508903,  1.16994885]]), array([[ 0.6311397 , -0.19107921,  0.12321025],\n       [-0.15656963,  0.15280228,  0.29522213],\n       [-0.90987643,  0.97699609, -1.53426743],\n       [-0.03290443, -0.04625833,  0.00650608],\n       [ 0.0845055 ,  0.05173286,  0.02031466],\n       [ 0.0417075 , -0.02306781, -0.08716545],\n       [ 0.05174543,  0.11100871,  0.31288194],\n       [ 0.06314244,  0.25273448, -0.12395387],\n       [ 0.22766819, -1.28620944,  0.9877496 ]]), array([[ 0.51625672, -0.15439624,  0.10035916],\n       [-0.12739764,  0.13708079,  0.24119501],\n       [-0.72817099,  0.7993019 , -1.24293187],\n       [-0.0272676 , -0.03915126,  0.0043992 ],\n       [ 0.07359385,  0.03887825,  0.01873847],\n       [ 0.0321731 , -0.0192432 , -0.07113834],\n       [ 0.04019429,  0.09082712,  0.25361993],\n       [ 0.04902388,  0.20864157, -0.10326029],\n       [ 0.17226448, -1.06306197,  0.79963484]]), array([[ 0.39662759, -0.11675023,  0.07706432],\n       [-0.09868694,  0.11736274,  0.18449079],\n       [-0.54554726,  0.61047822, -0.94282968],\n       [-0.0215646 , -0.03210816,  0.00230589],\n       [ 0.06279801,  0.02621558,  0.01736848],\n       [ 0.02268479, -0.01538343, -0.05515104],\n       [ 0.02869576,  0.0701568 ,  0.19400893],\n       [ 0.03499138,  0.16446133, -0.08247081],\n       [ 0.1207788 , -0.82537305,  0.60593598]]), array([[ 2.72126966e-01, -7.81031049e-02,  5.33296712e-02],\n       [-7.04302568e-02,  9.34082450e-02,  1.25083110e-01],\n       [-3.62501030e-01,  4.10536105e-01, -6.34343846e-01],\n       [-1.57962762e-02, -2.51308696e-02,  2.25441828e-04],\n       [ 5.21205923e-02,  1.37399779e-02,  1.62055502e-02],\n       [ 1.32409912e-02, -1.14885107e-02, -3.92059250e-02],\n       [ 1.72543358e-02,  4.89951751e-02,  1.34057431e-01],\n       [ 2.10326922e-02,  1.20183456e-01, -6.15948693e-02],\n       [ 7.38141587e-02, -5.72924375e-01,  4.07040139e-01]]), array([[ 0.14262671, -0.03841547,  0.02915978],\n       [-0.04261703,  0.06496749,  0.06294815],\n       [-0.17954093,  0.19955894, -0.31790154],\n       [-0.0099636 , -0.01822126, -0.00184291],\n       [ 0.04156403,  0.0014463 ,  0.01525048],\n       [ 0.00383992, -0.00755851, -0.02330542],\n       [ 0.00587453,  0.02733986,  0.0737741 ],\n       [ 0.00713507,  0.07579628, -0.04064175],\n       [ 0.03199724, -0.30556132,  0.20339308]]), array([[ 0.00799568,  0.00235342,  0.00455986],\n       [-0.01523284,  0.03178055, -0.00193481],\n       [ 0.00281613, -0.02229405,  0.00602259],\n       [-0.00406765, -0.01138125, -0.00389995],\n       [ 0.03113056, -0.01067082,  0.01450401],\n       [-0.00552043, -0.00359348, -0.00745196],\n       [-0.00543915,  0.00518864,  0.01316787],\n       [-0.00671464,  0.03128675, -0.01962056],\n       [-0.00402465, -0.02320133, -0.00449853]])], converged=False, n_iterations=200, activation_energy=np.float64(0.0), reaction_energy=np.float64(-0.14762840388812037), transition_state_index=np.int64(0), transition_state=Atoms(symbols='C2OH6', pbc=False), reaction_coordinate=array([0.        , 0.00272719, 0.00545438, 0.00818156, 0.01090875,\n       0.01363594, 0.01636313, 0.01909032]), method='NEB', calculation_time=53.7537899017334), properties={'step_type': 'elementary', 'reversible': True})], graph=<networkx.classes.digraph.DiGraph object at 0x114601400>, properties={'n_nodes': 2, 'n_edges': 1, 'node_types': ['reactants', 'products'], 'energy_span': np.float64(0.0), 'max_barrier': np.float64(2949.065732843227), 'min_rate': np.float64(0.0)})"}