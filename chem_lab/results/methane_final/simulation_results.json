{"predicted_products": "PredictionResult(products=['C=O', 'O'], confidence=0.7, method='RDKit', reasoning=None, alternative_products=None, reaction_type='methane_combustion')", "feasibility": "FeasibilityResult(is_feasible=np.True_, thermodynamic_feasible=np.True_, kinetic_feasible=np.True_, delta_h=np.float64(-3.5426590531114925), delta_g=np.float64(-3.5426590531114925), activation_barrier_estimate=np.float64(1.0085318106222985), equilibrium_constant=np.float64(7.685095239110312e+59), reaction_rate_estimate=np.float64(8.945906779913553e-05), temperature_range=(200.0, 800.0), pressure_sensitivity='pressure_insensitive', solvent_effects={'solvent': 'vacuum', 'polarity_effect': 'none', 'solvation_energy_change': 0.0, 'recommended_solvents': ['water', 'ethanol', 'acetone']}, recommendations=['Consider using appropriate solvent'], confidence=0.8999999999999999)", "reaction_pathway": "PathwayResult(images=[Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...))], energies=array([-5.18528749e+03, -5.15733283e+03, -4.76948268e+03, -4.67642606e+06,\n       -4.58071084e+03, -4.99429310e+03, -5.16365891e+03, -4.84199961e+03]), forces=[array([[ 4.01863931e-04, -1.24389747e-06, -2.00497903e-05],\n       [-2.01850444e-03,  2.52692713e-03, -2.80934343e-04],\n       [-1.18512927e-03, -2.43578273e-03, -1.70022812e-03],\n       [ 2.27710288e-04, -8.30583809e-04,  2.96535081e-03],\n       [ 2.63315852e-03,  7.26285502e-04, -1.02308718e-03],\n       [ 1.03883811e-01,  4.01982119e-08, -4.85730470e-07],\n       [-1.03883931e-01, -9.62653825e-09,  5.66778204e-07]]), array([[ 1.52252029e+01, -6.97216276e+00,  7.56053843e+00],\n       [-8.51009952e+00,  1.94655373e+01, -1.95933244e+00],\n       [-5.30222938e+00, -1.09233676e+01, -1.04609222e+01],\n       [ 1.37934935e+00, -1.24985858e+00,  4.62726395e+00],\n       [-2.79219014e+00, -3.20229895e-01,  2.32260722e-01],\n       [ 1.44955706e+02,  7.28890472e-01, -2.38020861e-06],\n       [-1.44955636e+02, -7.28899526e-01,  7.04554021e-06]]), array([[ 5.05765221e+01, -7.62515582e+01,  5.01778336e+01],\n       [-1.78523945e+01,  1.19275433e+02, -1.00095006e+01],\n       [-3.97997421e+01, -3.87835495e+01, -5.38828478e+01],\n       [ 6.25063558e+00, -4.33841112e+00,  1.37403456e+01],\n       [ 1.27856111e+00,  8.56945246e-02, -1.51473327e-02],\n       [ 1.27521307e+03,  2.38638077e+01,  3.82785140e-01],\n       [-1.27566766e+03, -2.38517588e+01, -3.93362565e-01]]), array([[ 3.61777591e+01, -1.93252115e+02,  8.17463053e+01],\n       [ 6.21269239e+01,  2.16361079e+02, -2.39076872e+01],\n       [-9.34307338e+01, -1.16272372e+01, -6.98733121e+01],\n       [-5.20549961e+00, -1.15197511e+01,  1.20414098e+01],\n       [ 8.97783888e-01,  3.22369503e-02,  3.45201365e-03],\n       [-4.24648770e+08, -1.32798340e+08,  9.18101154e+00],\n       [ 4.24713734e+08,  1.32793343e+08, -9.18716710e+00]]), array([[-7.56837312e+01, -1.64336109e+02,  7.82611944e+01],\n       [ 1.89689407e+02,  1.18742290e+02, -2.05449610e+01],\n       [-1.12010039e+02,  6.04196548e+01, -6.88981230e+01],\n       [-1.73412921e+00, -1.48131718e+01,  1.11811865e+01],\n       [-2.58242982e-01, -1.28732106e-02,  9.62863152e-04],\n       [-3.65584911e+03,  1.71860658e+02, -3.16509584e-02],\n       [ 3.65584520e+03, -1.71861134e+02,  3.14780019e-02]]), array([[ 2.63230813e+01, -3.72830541e+01,  9.51664702e+00],\n       [ 8.00809774e+01,  1.38730061e+01, -5.98809287e+00],\n       [-6.82539967e+01,  2.15846812e+01, -4.47740070e+00],\n       [-3.71126304e+01,  1.85138917e+00,  9.38124290e-01],\n       [-2.49309899e+00,  2.37078487e-01, -4.85568068e-02],\n       [-6.47389396e+01,  1.58558487e+00,  5.78342864e-02],\n       [ 6.61944839e+01, -1.84886604e+00,  1.39314322e-03]]), array([[ 1.01815532, -6.80864317,  0.39961511],\n       [12.29838742, -0.45641151, -0.35128543],\n       [-8.05758842, 13.30965576, -1.37662371],\n       [-5.14964713, -6.04347207,  1.32809139],\n       [ 2.69317412,  3.88716465, -0.42021373],\n       [ 0.90375795, -8.09741541,  0.85995233],\n       [-3.70617688,  4.20899107, -0.43953521]]), array([[-1.82092377e+02,  2.02630312e+01,  2.79202122e+00],\n       [-9.00271019e+00,  3.29213137e+00,  1.25946434e-01],\n       [ 8.07902393e+01, -1.11702651e+02, -7.34980299e-01],\n       [ 1.12631579e+02,  8.81050959e+01, -2.18385999e+00],\n       [-9.44120437e+00,  2.33586193e+01,  1.22863041e-04],\n       [ 1.97207767e+00, -5.87798036e+00,  3.07247399e-04],\n       [ 5.14251057e+00, -1.74381836e+01,  4.12834777e-04]])], converged=False, n_iterations=200, activation_energy=np.float64(604.5766506918108), reaction_energy=np.float64(343.2878838655506), transition_state_index=np.int64(4), transition_state=Atoms(symbols='CH4O2', pbc=False), reaction_coordinate=array([0.        , 0.38676931, 0.77353863, 1.16030794, 1.54707726,\n       1.93384657, 2.32061589, 2.7073852 ]), method='NEB', calculation_time=54.430838108062744)", "thermodynamics": "ThermoKineticResult(temperature=298.15, pressure=1.0, delta_h=np.float64(343.2878838655506), delta_g=np.float64(342.3989496405506), delta_s=0.0029815, activation_energy=np.float64(604.5766506918108), activation_enthalpy=np.float64(604.5766506918108), activation_entropy=0.0029815, activation_free_energy=np.float64(603.6877164668108), rate_constant=np.float64(0.0), equilibrium_constant=np.float64(0.0), half_life=inf, arrhenius_parameters={'A': 10000000000000.0, 'Ea': np.float64(604.5766506918108), 'k_ref': np.float64(0.0)}, eyring_parameters={'delta_H_double_dagger': np.float64(604.5766506918108), 'delta_S_double_dagger': 0.0029815}, temperature_dependence={'temperatures': [198.14999999999998, 218.14999999999998, 238.14999999999998, 258.15, 278.15, 298.15, 318.15, 338.15, 358.15, 378.15, 398.15], 'rate_constants': [np.float64(0.0), np.float64(0.0), np.float64(0.0), np.float64(0.0), np.float64(0.0), np.float64(0.0), np.float64(0.0), np.float64(0.0), np.float64(0.0), np.float64(0.0), np.float64(0.0)], 'equilibrium_constants': [np.float64(0.0), np.float64(0.0), np.float64(0.0), np.float64(0.0), np.float64(0.0), np.float64(0.0), np.float64(0.0), np.float64(0.0), np.float64(0.0), np.float64(0.0), np.float64(0.0)], 'delta_g_values': [np.float64(342.8952496405506), np.float64(342.8119896405506), np.float64(342.7207296405506), np.float64(342.62146964055063), np.float64(342.51420964055063), np.float64(342.3989496405506), np.float64(342.27568964055064), np.float64(342.1444296405506), np.float64(342.0051696405506), np.float64(341.8579096405506), np.float64(341.7026496405506)]})", "reaction_network": "ReactionNetwork(nodes={'reactants_0': NetworkNode(id='reactants_0', smiles='CH4.O2', atoms=Atoms(symbols='CH4O2', pbc=False), energy=0.0, properties={'n_atoms': 7, 'formula': 'CH4O2', 'n_molecules': 2, 'node_type': 'reactants'}), 'products_1': NetworkNode(id='products_1', smiles='C=O.O', atoms=Atoms(symbols='COH2OH2', pbc=False), energy=0.0, properties={'n_molecules': 2, 'node_type': 'products', 'smiles_list': ['C=O', 'O'], 'n_atoms': 7, 'formula': 'CH4O2'}), 'intermediate_0_2': NetworkNode(id='intermediate_0_2', smiles='CH4O2', atoms=Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), energy=np.float64(-4676426.060155071), properties={'n_atoms': 7, 'formula': 'CH4O2', 'n_molecules': 1, 'node_type': 'intermediate_0'}), 'intermediate_1_3': NetworkNode(id='intermediate_1_3', smiles='CH4O2', atoms=Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), energy=np.float64(-5163.658910133352), properties={'n_atoms': 7, 'formula': 'CH4O2', 'n_molecules': 1, 'node_type': 'intermediate_1'})}, edges=[NetworkEdge(source='reactants_0', target='products_1', activation_energy=0.5, reaction_energy=0.0, rate_constant=np.float64(35312.11242734564), pathway=PathwayResult(images=[Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...))], energies=array([-5.18528749e+03, -5.15733283e+03, -4.76948268e+03, -4.67642606e+06,\n       -4.58071084e+03, -4.99429310e+03, -5.16365891e+03, -4.84199961e+03]), forces=[array([[ 4.01863931e-04, -1.24389747e-06, -2.00497903e-05],\n       [-2.01850444e-03,  2.52692713e-03, -2.80934343e-04],\n       [-1.18512927e-03, -2.43578273e-03, -1.70022812e-03],\n       [ 2.27710288e-04, -8.30583809e-04,  2.96535081e-03],\n       [ 2.63315852e-03,  7.26285502e-04, -1.02308718e-03],\n       [ 1.03883811e-01,  4.01982119e-08, -4.85730470e-07],\n       [-1.03883931e-01, -9.62653825e-09,  5.66778204e-07]]), array([[ 1.52252029e+01, -6.97216276e+00,  7.56053843e+00],\n       [-8.51009952e+00,  1.94655373e+01, -1.95933244e+00],\n       [-5.30222938e+00, -1.09233676e+01, -1.04609222e+01],\n       [ 1.37934935e+00, -1.24985858e+00,  4.62726395e+00],\n       [-2.79219014e+00, -3.20229895e-01,  2.32260722e-01],\n       [ 1.44955706e+02,  7.28890472e-01, -2.38020861e-06],\n       [-1.44955636e+02, -7.28899526e-01,  7.04554021e-06]]), array([[ 5.05765221e+01, -7.62515582e+01,  5.01778336e+01],\n       [-1.78523945e+01,  1.19275433e+02, -1.00095006e+01],\n       [-3.97997421e+01, -3.87835495e+01, -5.38828478e+01],\n       [ 6.25063558e+00, -4.33841112e+00,  1.37403456e+01],\n       [ 1.27856111e+00,  8.56945246e-02, -1.51473327e-02],\n       [ 1.27521307e+03,  2.38638077e+01,  3.82785140e-01],\n       [-1.27566766e+03, -2.38517588e+01, -3.93362565e-01]]), array([[ 3.61777591e+01, -1.93252115e+02,  8.17463053e+01],\n       [ 6.21269239e+01,  2.16361079e+02, -2.39076872e+01],\n       [-9.34307338e+01, -1.16272372e+01, -6.98733121e+01],\n       [-5.20549961e+00, -1.15197511e+01,  1.20414098e+01],\n       [ 8.97783888e-01,  3.22369503e-02,  3.45201365e-03],\n       [-4.24648770e+08, -1.32798340e+08,  9.18101154e+00],\n       [ 4.24713734e+08,  1.32793343e+08, -9.18716710e+00]]), array([[-7.56837312e+01, -1.64336109e+02,  7.82611944e+01],\n       [ 1.89689407e+02,  1.18742290e+02, -2.05449610e+01],\n       [-1.12010039e+02,  6.04196548e+01, -6.88981230e+01],\n       [-1.73412921e+00, -1.48131718e+01,  1.11811865e+01],\n       [-2.58242982e-01, -1.28732106e-02,  9.62863152e-04],\n       [-3.65584911e+03,  1.71860658e+02, -3.16509584e-02],\n       [ 3.65584520e+03, -1.71861134e+02,  3.14780019e-02]]), array([[ 2.63230813e+01, -3.72830541e+01,  9.51664702e+00],\n       [ 8.00809774e+01,  1.38730061e+01, -5.98809287e+00],\n       [-6.82539967e+01,  2.15846812e+01, -4.47740070e+00],\n       [-3.71126304e+01,  1.85138917e+00,  9.38124290e-01],\n       [-2.49309899e+00,  2.37078487e-01, -4.85568068e-02],\n       [-6.47389396e+01,  1.58558487e+00,  5.78342864e-02],\n       [ 6.61944839e+01, -1.84886604e+00,  1.39314322e-03]]), array([[ 1.01815532, -6.80864317,  0.39961511],\n       [12.29838742, -0.45641151, -0.35128543],\n       [-8.05758842, 13.30965576, -1.37662371],\n       [-5.14964713, -6.04347207,  1.32809139],\n       [ 2.69317412,  3.88716465, -0.42021373],\n       [ 0.90375795, -8.09741541,  0.85995233],\n       [-3.70617688,  4.20899107, -0.43953521]]), array([[-1.82092377e+02,  2.02630312e+01,  2.79202122e+00],\n       [-9.00271019e+00,  3.29213137e+00,  1.25946434e-01],\n       [ 8.07902393e+01, -1.11702651e+02, -7.34980299e-01],\n       [ 1.12631579e+02,  8.81050959e+01, -2.18385999e+00],\n       [-9.44120437e+00,  2.33586193e+01,  1.22863041e-04],\n       [ 1.97207767e+00, -5.87798036e+00,  3.07247399e-04],\n       [ 5.14251057e+00, -1.74381836e+01,  4.12834777e-04]])], converged=False, n_iterations=200, activation_energy=np.float64(604.5766506918108), reaction_energy=np.float64(343.2878838655506), transition_state_index=np.int64(4), transition_state=Atoms(symbols='CH4O2', pbc=False), reaction_coordinate=array([0.        , 0.38676931, 0.77353863, 1.16030794, 1.54707726,\n       1.93384657, 2.32061589, 2.7073852 ]), method='NEB', calculation_time=54.430838108062744), properties={'step_type': 'elementary', 'reversible': True}), NetworkEdge(source='products_1', target='intermediate_0_2', activation_energy=np.float64(3273498.24210855), reaction_energy=np.float64(-4676426.060155071), rate_constant=np.float64(0.0), pathway=PathwayResult(images=[Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...))], energies=array([-5.18528749e+03, -5.15733283e+03, -4.76948268e+03, -4.67642606e+06,\n       -4.58071084e+03, -4.99429310e+03, -5.16365891e+03, -4.84199961e+03]), forces=[array([[ 4.01863931e-04, -1.24389747e-06, -2.00497903e-05],\n       [-2.01850444e-03,  2.52692713e-03, -2.80934343e-04],\n       [-1.18512927e-03, -2.43578273e-03, -1.70022812e-03],\n       [ 2.27710288e-04, -8.30583809e-04,  2.96535081e-03],\n       [ 2.63315852e-03,  7.26285502e-04, -1.02308718e-03],\n       [ 1.03883811e-01,  4.01982119e-08, -4.85730470e-07],\n       [-1.03883931e-01, -9.62653825e-09,  5.66778204e-07]]), array([[ 1.52252029e+01, -6.97216276e+00,  7.56053843e+00],\n       [-8.51009952e+00,  1.94655373e+01, -1.95933244e+00],\n       [-5.30222938e+00, -1.09233676e+01, -1.04609222e+01],\n       [ 1.37934935e+00, -1.24985858e+00,  4.62726395e+00],\n       [-2.79219014e+00, -3.20229895e-01,  2.32260722e-01],\n       [ 1.44955706e+02,  7.28890472e-01, -2.38020861e-06],\n       [-1.44955636e+02, -7.28899526e-01,  7.04554021e-06]]), array([[ 5.05765221e+01, -7.62515582e+01,  5.01778336e+01],\n       [-1.78523945e+01,  1.19275433e+02, -1.00095006e+01],\n       [-3.97997421e+01, -3.87835495e+01, -5.38828478e+01],\n       [ 6.25063558e+00, -4.33841112e+00,  1.37403456e+01],\n       [ 1.27856111e+00,  8.56945246e-02, -1.51473327e-02],\n       [ 1.27521307e+03,  2.38638077e+01,  3.82785140e-01],\n       [-1.27566766e+03, -2.38517588e+01, -3.93362565e-01]]), array([[ 3.61777591e+01, -1.93252115e+02,  8.17463053e+01],\n       [ 6.21269239e+01,  2.16361079e+02, -2.39076872e+01],\n       [-9.34307338e+01, -1.16272372e+01, -6.98733121e+01],\n       [-5.20549961e+00, -1.15197511e+01,  1.20414098e+01],\n       [ 8.97783888e-01,  3.22369503e-02,  3.45201365e-03],\n       [-4.24648770e+08, -1.32798340e+08,  9.18101154e+00],\n       [ 4.24713734e+08,  1.32793343e+08, -9.18716710e+00]]), array([[-7.56837312e+01, -1.64336109e+02,  7.82611944e+01],\n       [ 1.89689407e+02,  1.18742290e+02, -2.05449610e+01],\n       [-1.12010039e+02,  6.04196548e+01, -6.88981230e+01],\n       [-1.73412921e+00, -1.48131718e+01,  1.11811865e+01],\n       [-2.58242982e-01, -1.28732106e-02,  9.62863152e-04],\n       [-3.65584911e+03,  1.71860658e+02, -3.16509584e-02],\n       [ 3.65584520e+03, -1.71861134e+02,  3.14780019e-02]]), array([[ 2.63230813e+01, -3.72830541e+01,  9.51664702e+00],\n       [ 8.00809774e+01,  1.38730061e+01, -5.98809287e+00],\n       [-6.82539967e+01,  2.15846812e+01, -4.47740070e+00],\n       [-3.71126304e+01,  1.85138917e+00,  9.38124290e-01],\n       [-2.49309899e+00,  2.37078487e-01, -4.85568068e-02],\n       [-6.47389396e+01,  1.58558487e+00,  5.78342864e-02],\n       [ 6.61944839e+01, -1.84886604e+00,  1.39314322e-03]]), array([[ 1.01815532, -6.80864317,  0.39961511],\n       [12.29838742, -0.45641151, -0.35128543],\n       [-8.05758842, 13.30965576, -1.37662371],\n       [-5.14964713, -6.04347207,  1.32809139],\n       [ 2.69317412,  3.88716465, -0.42021373],\n       [ 0.90375795, -8.09741541,  0.85995233],\n       [-3.70617688,  4.20899107, -0.43953521]]), array([[-1.82092377e+02,  2.02630312e+01,  2.79202122e+00],\n       [-9.00271019e+00,  3.29213137e+00,  1.25946434e-01],\n       [ 8.07902393e+01, -1.11702651e+02, -7.34980299e-01],\n       [ 1.12631579e+02,  8.81050959e+01, -2.18385999e+00],\n       [-9.44120437e+00,  2.33586193e+01,  1.22863041e-04],\n       [ 1.97207767e+00, -5.87798036e+00,  3.07247399e-04],\n       [ 5.14251057e+00, -1.74381836e+01,  4.12834777e-04]])], converged=False, n_iterations=200, activation_energy=np.float64(604.5766506918108), reaction_energy=np.float64(343.2878838655506), transition_state_index=np.int64(4), transition_state=Atoms(symbols='CH4O2', pbc=False), reaction_coordinate=array([0.        , 0.38676931, 0.77353863, 1.16030794, 1.54707726,\n       1.93384657, 2.32061589, 2.7073852 ]), method='NEB', calculation_time=54.430838108062744), properties={'step_type': 'elementary', 'reversible': True}), NetworkEdge(source='intermediate_0_2', target='intermediate_1_3', activation_energy=np.float64(3269883.680871457), reaction_energy=np.float64(4671262.401244938), rate_constant=np.float64(0.0), pathway=PathwayResult(images=[Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...)), Atoms(symbols='CH4O2', pbc=False, calculator=PySCF(...))], energies=array([-5.18528749e+03, -5.15733283e+03, -4.76948268e+03, -4.67642606e+06,\n       -4.58071084e+03, -4.99429310e+03, -5.16365891e+03, -4.84199961e+03]), forces=[array([[ 4.01863931e-04, -1.24389747e-06, -2.00497903e-05],\n       [-2.01850444e-03,  2.52692713e-03, -2.80934343e-04],\n       [-1.18512927e-03, -2.43578273e-03, -1.70022812e-03],\n       [ 2.27710288e-04, -8.30583809e-04,  2.96535081e-03],\n       [ 2.63315852e-03,  7.26285502e-04, -1.02308718e-03],\n       [ 1.03883811e-01,  4.01982119e-08, -4.85730470e-07],\n       [-1.03883931e-01, -9.62653825e-09,  5.66778204e-07]]), array([[ 1.52252029e+01, -6.97216276e+00,  7.56053843e+00],\n       [-8.51009952e+00,  1.94655373e+01, -1.95933244e+00],\n       [-5.30222938e+00, -1.09233676e+01, -1.04609222e+01],\n       [ 1.37934935e+00, -1.24985858e+00,  4.62726395e+00],\n       [-2.79219014e+00, -3.20229895e-01,  2.32260722e-01],\n       [ 1.44955706e+02,  7.28890472e-01, -2.38020861e-06],\n       [-1.44955636e+02, -7.28899526e-01,  7.04554021e-06]]), array([[ 5.05765221e+01, -7.62515582e+01,  5.01778336e+01],\n       [-1.78523945e+01,  1.19275433e+02, -1.00095006e+01],\n       [-3.97997421e+01, -3.87835495e+01, -5.38828478e+01],\n       [ 6.25063558e+00, -4.33841112e+00,  1.37403456e+01],\n       [ 1.27856111e+00,  8.56945246e-02, -1.51473327e-02],\n       [ 1.27521307e+03,  2.38638077e+01,  3.82785140e-01],\n       [-1.27566766e+03, -2.38517588e+01, -3.93362565e-01]]), array([[ 3.61777591e+01, -1.93252115e+02,  8.17463053e+01],\n       [ 6.21269239e+01,  2.16361079e+02, -2.39076872e+01],\n       [-9.34307338e+01, -1.16272372e+01, -6.98733121e+01],\n       [-5.20549961e+00, -1.15197511e+01,  1.20414098e+01],\n       [ 8.97783888e-01,  3.22369503e-02,  3.45201365e-03],\n       [-4.24648770e+08, -1.32798340e+08,  9.18101154e+00],\n       [ 4.24713734e+08,  1.32793343e+08, -9.18716710e+00]]), array([[-7.56837312e+01, -1.64336109e+02,  7.82611944e+01],\n       [ 1.89689407e+02,  1.18742290e+02, -2.05449610e+01],\n       [-1.12010039e+02,  6.04196548e+01, -6.88981230e+01],\n       [-1.73412921e+00, -1.48131718e+01,  1.11811865e+01],\n       [-2.58242982e-01, -1.28732106e-02,  9.62863152e-04],\n       [-3.65584911e+03,  1.71860658e+02, -3.16509584e-02],\n       [ 3.65584520e+03, -1.71861134e+02,  3.14780019e-02]]), array([[ 2.63230813e+01, -3.72830541e+01,  9.51664702e+00],\n       [ 8.00809774e+01,  1.38730061e+01, -5.98809287e+00],\n       [-6.82539967e+01,  2.15846812e+01, -4.47740070e+00],\n       [-3.71126304e+01,  1.85138917e+00,  9.38124290e-01],\n       [-2.49309899e+00,  2.37078487e-01, -4.85568068e-02],\n       [-6.47389396e+01,  1.58558487e+00,  5.78342864e-02],\n       [ 6.61944839e+01, -1.84886604e+00,  1.39314322e-03]]), array([[ 1.01815532, -6.80864317,  0.39961511],\n       [12.29838742, -0.45641151, -0.35128543],\n       [-8.05758842, 13.30965576, -1.37662371],\n       [-5.14964713, -6.04347207,  1.32809139],\n       [ 2.69317412,  3.88716465, -0.42021373],\n       [ 0.90375795, -8.09741541,  0.85995233],\n       [-3.70617688,  4.20899107, -0.43953521]]), array([[-1.82092377e+02,  2.02630312e+01,  2.79202122e+00],\n       [-9.00271019e+00,  3.29213137e+00,  1.25946434e-01],\n       [ 8.07902393e+01, -1.11702651e+02, -7.34980299e-01],\n       [ 1.12631579e+02,  8.81050959e+01, -2.18385999e+00],\n       [-9.44120437e+00,  2.33586193e+01,  1.22863041e-04],\n       [ 1.97207767e+00, -5.87798036e+00,  3.07247399e-04],\n       [ 5.14251057e+00, -1.74381836e+01,  4.12834777e-04]])], converged=False, n_iterations=200, activation_energy=np.float64(604.5766506918108), reaction_energy=np.float64(343.2878838655506), transition_state_index=np.int64(4), transition_state=Atoms(symbols='CH4O2', pbc=False), reaction_coordinate=array([0.        , 0.38676931, 0.77353863, 1.16030794, 1.54707726,\n       1.93384657, 2.32061589, 2.7073852 ]), method='NEB', calculation_time=54.430838108062744), properties={'step_type': 'elementary', 'reversible': True})], graph=<networkx.classes.digraph.DiGraph object at 0x122101010>, properties={'n_nodes': 4, 'n_edges': 3, 'node_types': ['products', 'reactants', 'intermediate_0', 'intermediate_1'], 'energy_span': np.float64(4671262.401244938), 'max_barrier': np.float64(3273498.24210855), 'min_rate': np.float64(0.0)})"}