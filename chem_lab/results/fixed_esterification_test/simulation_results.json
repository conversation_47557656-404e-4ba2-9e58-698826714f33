{"predicted_products": "PredictionResult(products=['O'], confidence=0.7, method='ML-MolecularTransformer', reasoning='ML prediction using MolecularTransformer', alternative_products=None, reaction_type='ml_predicted')", "feasibility": "FeasibilityResult(is_feasible=True, thermodynamic_feasible=True, kinetic_feasible=True, delta_h=-3.0, delta_g=-2.5, activation_barrier_estimate=1.2, equilibrium_constant=np.float64(1.8213000827632105e+42), reaction_rate_estimate=np.float64(5.188201996502146e-08), temperature_range=(200.0, 800.0), pressure_sensitivity='low', solvent_effects={'solvent': 'vacuum', 'effect': 'estimated'}, recommendations=[], confidence=np.float64(0.7000000000000001), ml_predictions={'feasibility': SurrogateResult(prediction=True, confidence=0.8, model_name='BayesianReactivityPredictor', computation_time=1.3113021850585938e-05, uncertainty=0.1, metadata=None), 'activation_energy': SurrogateResult(prediction=1.2, confidence=0.7, model_name='CoeffNetActivationPredictor', computation_time=5.0067901611328125e-06, uncertainty=None, metadata=None), 'yield': SurrogateResult(prediction=0.75, confidence=0.6, model_name='EgretYieldPredictor', computation_time=5.7220458984375e-06, uncertainty=None, metadata=None)}, computation_method='ML', prediction_uncertainties={'feasibility': 0.1, 'activation_energy': None})", "reaction_pathway": "PathwayResult(images=[Atoms(symbols='C2OH6C2O2H4', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6C2O2H4', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6C2O2H4', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6C2O2H4', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6C2O2H4', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6C2O2H4', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6C2O2H4', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6C2O2H4', pbc=False, calculator=PySCF(...))], energies=array([-10439.56182834, -10439.56182834, -10439.56182834, -10439.56182834,\n       -10439.56182834, -10439.56182834, -10439.56182834, -10439.56182834]), forces=[array([[ 0.00804827,  0.00234602,  0.00454169],\n       [-0.01537202,  0.03184232, -0.00193048],\n       [ 0.00298569, -0.02233459,  0.00599042],\n       [-0.0040747 , -0.01141188, -0.00388261],\n       [ 0.03113595, -0.01065843,  0.01450924],\n       [-0.00552291, -0.00359577, -0.00746571],\n       [-0.00542904,  0.00518453,  0.01313909],\n       [-0.00671972,  0.03125997, -0.01959652],\n       [-0.00409733, -0.02316552, -0.00446058],\n       [ 0.02987864, -0.00534025,  0.00967256],\n       [ 0.02137226,  0.00504162,  0.00503082],\n       [-0.0151609 ,  0.03475128, -0.01236499],\n       [-0.01058149, -0.00825199, -0.0005047 ],\n       [-0.01394811, -0.01858484,  0.00190225],\n       [-0.00331532,  0.01317583,  0.01551633],\n       [ 0.00643353,  0.00253958, -0.01941947],\n       [-0.01694703, -0.02628142,  0.00391667]]), array([[ 0.00804827,  0.00234602,  0.00454169],\n       [-0.01537202,  0.03184232, -0.00193048],\n       [ 0.00298569, -0.02233459,  0.00599042],\n       [-0.0040747 , -0.01141188, -0.00388261],\n       [ 0.03113595, -0.01065843,  0.01450924],\n       [-0.00552291, -0.00359577, -0.00746571],\n       [-0.00542904,  0.00518453,  0.01313909],\n       [-0.00671972,  0.03125997, -0.01959652],\n       [-0.00409733, -0.02316552, -0.00446058],\n       [ 0.02987864, -0.00534025,  0.00967256],\n       [ 0.02137226,  0.00504162,  0.00503082],\n       [-0.0151609 ,  0.03475128, -0.01236499],\n       [-0.01058149, -0.00825199, -0.0005047 ],\n       [-0.01394811, -0.01858484,  0.00190225],\n       [-0.00331532,  0.01317583,  0.01551633],\n       [ 0.00643353,  0.00253958, -0.01941947],\n       [-0.01694703, -0.02628142,  0.00391667]]), array([[ 0.00804827,  0.00234602,  0.00454169],\n       [-0.01537202,  0.03184232, -0.00193048],\n       [ 0.00298569, -0.02233459,  0.00599042],\n       [-0.0040747 , -0.01141188, -0.00388261],\n       [ 0.03113595, -0.01065843,  0.01450924],\n       [-0.00552291, -0.00359577, -0.00746571],\n       [-0.00542904,  0.00518453,  0.01313909],\n       [-0.00671972,  0.03125997, -0.01959652],\n       [-0.00409733, -0.02316552, -0.00446058],\n       [ 0.02987864, -0.00534025,  0.00967256],\n       [ 0.02137226,  0.00504162,  0.00503082],\n       [-0.0151609 ,  0.03475128, -0.01236499],\n       [-0.01058149, -0.00825199, -0.0005047 ],\n       [-0.01394811, -0.01858484,  0.00190225],\n       [-0.00331532,  0.01317583,  0.01551633],\n       [ 0.00643353,  0.00253958, -0.01941947],\n       [-0.01694703, -0.02628142,  0.00391667]]), array([[ 0.00804827,  0.00234602,  0.00454169],\n       [-0.01537202,  0.03184232, -0.00193048],\n       [ 0.00298569, -0.02233459,  0.00599042],\n       [-0.0040747 , -0.01141188, -0.00388261],\n       [ 0.03113595, -0.01065843,  0.01450924],\n       [-0.00552291, -0.00359577, -0.00746571],\n       [-0.00542904,  0.00518453,  0.01313909],\n       [-0.00671972,  0.03125997, -0.01959652],\n       [-0.00409733, -0.02316552, -0.00446058],\n       [ 0.02987864, -0.00534025,  0.00967256],\n       [ 0.02137226,  0.00504162,  0.00503082],\n       [-0.0151609 ,  0.03475128, -0.01236499],\n       [-0.01058149, -0.00825199, -0.0005047 ],\n       [-0.01394811, -0.01858484,  0.00190225],\n       [-0.00331532,  0.01317583,  0.01551633],\n       [ 0.00643353,  0.00253958, -0.01941947],\n       [-0.01694703, -0.02628142,  0.00391667]]), array([[ 0.00804827,  0.00234602,  0.00454169],\n       [-0.01537202,  0.03184232, -0.00193048],\n       [ 0.00298569, -0.02233459,  0.00599042],\n       [-0.0040747 , -0.01141188, -0.00388261],\n       [ 0.03113595, -0.01065843,  0.01450924],\n       [-0.00552291, -0.00359577, -0.00746571],\n       [-0.00542904,  0.00518453,  0.01313909],\n       [-0.00671972,  0.03125997, -0.01959652],\n       [-0.00409733, -0.02316552, -0.00446058],\n       [ 0.02987864, -0.00534025,  0.00967256],\n       [ 0.02137226,  0.00504162,  0.00503082],\n       [-0.0151609 ,  0.03475128, -0.01236499],\n       [-0.01058149, -0.00825199, -0.0005047 ],\n       [-0.01394811, -0.01858484,  0.00190225],\n       [-0.00331532,  0.01317583,  0.01551633],\n       [ 0.00643353,  0.00253958, -0.01941947],\n       [-0.01694703, -0.02628142,  0.00391667]]), array([[ 0.00804827,  0.00234602,  0.00454169],\n       [-0.01537202,  0.03184232, -0.00193048],\n       [ 0.00298569, -0.02233459,  0.00599042],\n       [-0.0040747 , -0.01141188, -0.00388261],\n       [ 0.03113595, -0.01065843,  0.01450924],\n       [-0.00552291, -0.00359577, -0.00746571],\n       [-0.00542904,  0.00518453,  0.01313909],\n       [-0.00671972,  0.03125997, -0.01959652],\n       [-0.00409733, -0.02316552, -0.00446058],\n       [ 0.02987864, -0.00534025,  0.00967256],\n       [ 0.02137226,  0.00504162,  0.00503082],\n       [-0.0151609 ,  0.03475128, -0.01236499],\n       [-0.01058149, -0.00825199, -0.0005047 ],\n       [-0.01394811, -0.01858484,  0.00190225],\n       [-0.00331532,  0.01317583,  0.01551633],\n       [ 0.00643353,  0.00253958, -0.01941947],\n       [-0.01694703, -0.02628142,  0.00391667]]), array([[ 0.00804827,  0.00234602,  0.00454169],\n       [-0.01537202,  0.03184232, -0.00193048],\n       [ 0.00298569, -0.02233459,  0.00599042],\n       [-0.0040747 , -0.01141188, -0.00388261],\n       [ 0.03113595, -0.01065843,  0.01450924],\n       [-0.00552291, -0.00359577, -0.00746571],\n       [-0.00542904,  0.00518453,  0.01313909],\n       [-0.00671972,  0.03125997, -0.01959652],\n       [-0.00409733, -0.02316552, -0.00446058],\n       [ 0.02987864, -0.00534025,  0.00967256],\n       [ 0.02137226,  0.00504162,  0.00503082],\n       [-0.0151609 ,  0.03475128, -0.01236499],\n       [-0.01058149, -0.00825199, -0.0005047 ],\n       [-0.01394811, -0.01858484,  0.00190225],\n       [-0.00331532,  0.01317583,  0.01551633],\n       [ 0.00643353,  0.00253958, -0.01941947],\n       [-0.01694703, -0.02628142,  0.00391667]]), array([[ 0.00804827,  0.00234602,  0.00454169],\n       [-0.01537202,  0.03184232, -0.00193048],\n       [ 0.00298569, -0.02233459,  0.00599042],\n       [-0.0040747 , -0.01141188, -0.00388261],\n       [ 0.03113595, -0.01065843,  0.01450924],\n       [-0.00552291, -0.00359577, -0.00746571],\n       [-0.00542904,  0.00518453,  0.01313909],\n       [-0.00671972,  0.03125997, -0.01959652],\n       [-0.00409733, -0.02316552, -0.00446058],\n       [ 0.02987864, -0.00534025,  0.00967256],\n       [ 0.02137226,  0.00504162,  0.00503082],\n       [-0.0151609 ,  0.03475128, -0.01236499],\n       [-0.01058149, -0.00825199, -0.0005047 ],\n       [-0.01394811, -0.01858484,  0.00190225],\n       [-0.00331532,  0.01317583,  0.01551633],\n       [ 0.00643353,  0.00253958, -0.01941947],\n       [-0.01694703, -0.02628142,  0.00391667]])], converged=False, n_iterations=200, activation_energy=np.float64(0.0), reaction_energy=np.float64(0.0), transition_state_index=np.int64(0), transition_state=Atoms(symbols='C2OH6C2O2H4', pbc=False), reaction_coordinate=array([0., 0., 0., 0., 0., 0., 0., 0.]), method='NEB', calculation_time=12.94938588142395)", "thermodynamics": "ThermoKineticResult(temperature=298.15, pressure=1.0, delta_h=np.float64(0.0), delta_g=np.float64(-0.888934225), delta_s=0.0029815, activation_energy=np.float64(0.0), activation_enthalpy=np.float64(0.0), activation_entropy=0.0029815, activation_free_energy=np.float64(-0.888934225), rate_constant=np.float64(6.597216353378955e+27), equilibrium_constant=np.float64(1061936772796005.1), half_life=np.float64(1.0504430397300217e-28), arrhenius_parameters={'A': 10000000000000.0, 'Ea': np.float64(0.0), 'k_ref': np.float64(10000000000000.0)}, eyring_parameters={'delta_H_double_dagger': np.float64(0.0), 'delta_S_double_dagger': 0.0029815}, temperature_dependence={'temperatures': [198.14999999999998, 218.14999999999998, 238.14999999999998, 258.15, 278.15, 298.15, 318.15, 338.15, 358.15, 378.15, 398.15], 'rate_constants': [np.float64(4.000753597368431e+22), np.float64(4.485992902737767e+23), np.float64(4.987806395633994e+24), np.float64(5.506641055477155e+25), np.float64(6.042954723855029e+26), np.float64(6.597216353378955e+27), np.float64(7.169906262028387e+28), np.float64(7.761516393102539e+29), np.float64(8.372550580898865e+30), np.float64(9.003524822241591e+31), np.float64(9.65496755398594e+32)], 'equilibrium_constants': [np.float64(9689926311.607233), np.float64(98690673211.9531), np.float64(1005152017240.9857), np.float64(10237346092409.217), np.float64(104266074402793.16), np.float64(1061936772796005.1), np.float64(1.0815691641558304e+16), np.float64(1.1015645063055512e+17), np.float64(1.1219295092416028e+18), np.float64(1.1426710070104232e+19), np.float64(1.1637959600018145e+20)], 'delta_g_values': [np.float64(-0.3926342249999999), np.float64(-0.4758942249999999), np.float64(-0.5671542249999999), np.float64(-0.6664142249999999), np.float64(-0.7736742249999999), np.float64(-0.888934225), np.float64(-1.0121942249999998), np.float64(-1.143454225), np.float64(-1.2827142249999999), np.float64(-1.429974225), np.float64(-1.5852342249999998)]})", "reaction_network": "ReactionNetwork(nodes={'reactants_0': NetworkNode(id='reactants_0', smiles='C2H6O.C2H4O2', atoms=Atoms(symbols='C2OH6C2O2H4', pbc=False), energy=0.0, properties={'n_atoms': 17, 'formula': 'C4H10O3', 'n_molecules': 2, 'node_type': 'reactants'}), 'products_1': NetworkNode(id='products_1', smiles='O', atoms=Atoms(symbols='OH2', pbc=False), energy=0.0, properties={'n_molecules': 1, 'node_type': 'products', 'smiles_list': ['O'], 'n_atoms': 3, 'formula': 'H2O'})}, edges=[NetworkEdge(source='reactants_0', target='products_1', activation_energy=0.5, reaction_energy=0.0, rate_constant=np.float64(35312.11242734564), pathway=PathwayResult(images=[Atoms(symbols='C2OH6C2O2H4', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6C2O2H4', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6C2O2H4', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6C2O2H4', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6C2O2H4', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6C2O2H4', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6C2O2H4', pbc=False, calculator=PySCF(...)), Atoms(symbols='C2OH6C2O2H4', pbc=False, calculator=PySCF(...))], energies=array([-10439.56182834, -10439.56182834, -10439.56182834, -10439.56182834,\n       -10439.56182834, -10439.56182834, -10439.56182834, -10439.56182834]), forces=[array([[ 0.00804827,  0.00234602,  0.00454169],\n       [-0.01537202,  0.03184232, -0.00193048],\n       [ 0.00298569, -0.02233459,  0.00599042],\n       [-0.0040747 , -0.01141188, -0.00388261],\n       [ 0.03113595, -0.01065843,  0.01450924],\n       [-0.00552291, -0.00359577, -0.00746571],\n       [-0.00542904,  0.00518453,  0.01313909],\n       [-0.00671972,  0.03125997, -0.01959652],\n       [-0.00409733, -0.02316552, -0.00446058],\n       [ 0.02987864, -0.00534025,  0.00967256],\n       [ 0.02137226,  0.00504162,  0.00503082],\n       [-0.0151609 ,  0.03475128, -0.01236499],\n       [-0.01058149, -0.00825199, -0.0005047 ],\n       [-0.01394811, -0.01858484,  0.00190225],\n       [-0.00331532,  0.01317583,  0.01551633],\n       [ 0.00643353,  0.00253958, -0.01941947],\n       [-0.01694703, -0.02628142,  0.00391667]]), array([[ 0.00804827,  0.00234602,  0.00454169],\n       [-0.01537202,  0.03184232, -0.00193048],\n       [ 0.00298569, -0.02233459,  0.00599042],\n       [-0.0040747 , -0.01141188, -0.00388261],\n       [ 0.03113595, -0.01065843,  0.01450924],\n       [-0.00552291, -0.00359577, -0.00746571],\n       [-0.00542904,  0.00518453,  0.01313909],\n       [-0.00671972,  0.03125997, -0.01959652],\n       [-0.00409733, -0.02316552, -0.00446058],\n       [ 0.02987864, -0.00534025,  0.00967256],\n       [ 0.02137226,  0.00504162,  0.00503082],\n       [-0.0151609 ,  0.03475128, -0.01236499],\n       [-0.01058149, -0.00825199, -0.0005047 ],\n       [-0.01394811, -0.01858484,  0.00190225],\n       [-0.00331532,  0.01317583,  0.01551633],\n       [ 0.00643353,  0.00253958, -0.01941947],\n       [-0.01694703, -0.02628142,  0.00391667]]), array([[ 0.00804827,  0.00234602,  0.00454169],\n       [-0.01537202,  0.03184232, -0.00193048],\n       [ 0.00298569, -0.02233459,  0.00599042],\n       [-0.0040747 , -0.01141188, -0.00388261],\n       [ 0.03113595, -0.01065843,  0.01450924],\n       [-0.00552291, -0.00359577, -0.00746571],\n       [-0.00542904,  0.00518453,  0.01313909],\n       [-0.00671972,  0.03125997, -0.01959652],\n       [-0.00409733, -0.02316552, -0.00446058],\n       [ 0.02987864, -0.00534025,  0.00967256],\n       [ 0.02137226,  0.00504162,  0.00503082],\n       [-0.0151609 ,  0.03475128, -0.01236499],\n       [-0.01058149, -0.00825199, -0.0005047 ],\n       [-0.01394811, -0.01858484,  0.00190225],\n       [-0.00331532,  0.01317583,  0.01551633],\n       [ 0.00643353,  0.00253958, -0.01941947],\n       [-0.01694703, -0.02628142,  0.00391667]]), array([[ 0.00804827,  0.00234602,  0.00454169],\n       [-0.01537202,  0.03184232, -0.00193048],\n       [ 0.00298569, -0.02233459,  0.00599042],\n       [-0.0040747 , -0.01141188, -0.00388261],\n       [ 0.03113595, -0.01065843,  0.01450924],\n       [-0.00552291, -0.00359577, -0.00746571],\n       [-0.00542904,  0.00518453,  0.01313909],\n       [-0.00671972,  0.03125997, -0.01959652],\n       [-0.00409733, -0.02316552, -0.00446058],\n       [ 0.02987864, -0.00534025,  0.00967256],\n       [ 0.02137226,  0.00504162,  0.00503082],\n       [-0.0151609 ,  0.03475128, -0.01236499],\n       [-0.01058149, -0.00825199, -0.0005047 ],\n       [-0.01394811, -0.01858484,  0.00190225],\n       [-0.00331532,  0.01317583,  0.01551633],\n       [ 0.00643353,  0.00253958, -0.01941947],\n       [-0.01694703, -0.02628142,  0.00391667]]), array([[ 0.00804827,  0.00234602,  0.00454169],\n       [-0.01537202,  0.03184232, -0.00193048],\n       [ 0.00298569, -0.02233459,  0.00599042],\n       [-0.0040747 , -0.01141188, -0.00388261],\n       [ 0.03113595, -0.01065843,  0.01450924],\n       [-0.00552291, -0.00359577, -0.00746571],\n       [-0.00542904,  0.00518453,  0.01313909],\n       [-0.00671972,  0.03125997, -0.01959652],\n       [-0.00409733, -0.02316552, -0.00446058],\n       [ 0.02987864, -0.00534025,  0.00967256],\n       [ 0.02137226,  0.00504162,  0.00503082],\n       [-0.0151609 ,  0.03475128, -0.01236499],\n       [-0.01058149, -0.00825199, -0.0005047 ],\n       [-0.01394811, -0.01858484,  0.00190225],\n       [-0.00331532,  0.01317583,  0.01551633],\n       [ 0.00643353,  0.00253958, -0.01941947],\n       [-0.01694703, -0.02628142,  0.00391667]]), array([[ 0.00804827,  0.00234602,  0.00454169],\n       [-0.01537202,  0.03184232, -0.00193048],\n       [ 0.00298569, -0.02233459,  0.00599042],\n       [-0.0040747 , -0.01141188, -0.00388261],\n       [ 0.03113595, -0.01065843,  0.01450924],\n       [-0.00552291, -0.00359577, -0.00746571],\n       [-0.00542904,  0.00518453,  0.01313909],\n       [-0.00671972,  0.03125997, -0.01959652],\n       [-0.00409733, -0.02316552, -0.00446058],\n       [ 0.02987864, -0.00534025,  0.00967256],\n       [ 0.02137226,  0.00504162,  0.00503082],\n       [-0.0151609 ,  0.03475128, -0.01236499],\n       [-0.01058149, -0.00825199, -0.0005047 ],\n       [-0.01394811, -0.01858484,  0.00190225],\n       [-0.00331532,  0.01317583,  0.01551633],\n       [ 0.00643353,  0.00253958, -0.01941947],\n       [-0.01694703, -0.02628142,  0.00391667]]), array([[ 0.00804827,  0.00234602,  0.00454169],\n       [-0.01537202,  0.03184232, -0.00193048],\n       [ 0.00298569, -0.02233459,  0.00599042],\n       [-0.0040747 , -0.01141188, -0.00388261],\n       [ 0.03113595, -0.01065843,  0.01450924],\n       [-0.00552291, -0.00359577, -0.00746571],\n       [-0.00542904,  0.00518453,  0.01313909],\n       [-0.00671972,  0.03125997, -0.01959652],\n       [-0.00409733, -0.02316552, -0.00446058],\n       [ 0.02987864, -0.00534025,  0.00967256],\n       [ 0.02137226,  0.00504162,  0.00503082],\n       [-0.0151609 ,  0.03475128, -0.01236499],\n       [-0.01058149, -0.00825199, -0.0005047 ],\n       [-0.01394811, -0.01858484,  0.00190225],\n       [-0.00331532,  0.01317583,  0.01551633],\n       [ 0.00643353,  0.00253958, -0.01941947],\n       [-0.01694703, -0.02628142,  0.00391667]]), array([[ 0.00804827,  0.00234602,  0.00454169],\n       [-0.01537202,  0.03184232, -0.00193048],\n       [ 0.00298569, -0.02233459,  0.00599042],\n       [-0.0040747 , -0.01141188, -0.00388261],\n       [ 0.03113595, -0.01065843,  0.01450924],\n       [-0.00552291, -0.00359577, -0.00746571],\n       [-0.00542904,  0.00518453,  0.01313909],\n       [-0.00671972,  0.03125997, -0.01959652],\n       [-0.00409733, -0.02316552, -0.00446058],\n       [ 0.02987864, -0.00534025,  0.00967256],\n       [ 0.02137226,  0.00504162,  0.00503082],\n       [-0.0151609 ,  0.03475128, -0.01236499],\n       [-0.01058149, -0.00825199, -0.0005047 ],\n       [-0.01394811, -0.01858484,  0.00190225],\n       [-0.00331532,  0.01317583,  0.01551633],\n       [ 0.00643353,  0.00253958, -0.01941947],\n       [-0.01694703, -0.02628142,  0.00391667]])], converged=False, n_iterations=200, activation_energy=np.float64(0.0), reaction_energy=np.float64(0.0), transition_state_index=np.int64(0), transition_state=Atoms(symbols='C2OH6C2O2H4', pbc=False), reaction_coordinate=array([0., 0., 0., 0., 0., 0., 0., 0.]), method='NEB', calculation_time=12.94938588142395), properties={'step_type': 'elementary', 'reversible': True})], graph=<networkx.classes.digraph.DiGraph object at 0x11aa14980>, properties={'n_nodes': 2, 'n_edges': 1, 'node_types': ['reactants', 'products'], 'energy_span': 0.0, 'max_barrier': 0.5, 'min_rate': np.float64(35312.11242734564)})"}