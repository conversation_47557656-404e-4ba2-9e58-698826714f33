# 🧪 Virtual Chemistry Simulation Lab

A comprehensive Python toolkit for simulating realistic chemical reactions using quantum chemistry and AI techniques.

## 🎯 Features

- **Reaction Prediction**: Predict if a reaction will occur and what products will form
- **Product Prediction**: Use LLMs (OpenAI, Perplexity) and RDKit to predict reaction products
- **Reaction Pathways**: Model reaction mechanisms using NEB (Nudged Elastic Band) and Transition State Search
- **Thermodynamics & Kinetics**: Compute activation barriers, rate constants, and Gibbs energies
- **Visualization**: Interactive energy diagrams and molecular structure visualization
- **Network Analysis**: Build and analyze reaction network graphs
- **Flexible Input**: Accept any molecule as SMILES or XYZ format

## 🚀 Quick Start

### Installation

1. **Clone and setup environment:**
```bash
git clone <repository-url>
cd chem_lab
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Optional: Set up API keys for LLM integration:**
```bash
export OPENAI_API_KEY="your-openai-key"
export PERPLEXITY_API_KEY="your-perplexity-key"
```

### Basic Usage

```bash
# Run a complete reaction simulation
python main.py --reactants "CCO" "O=O" --temperature 298.15 --pressure 1.0

# Interactive mode
python main.py --interactive

# Load from file
python main.py --input-file examples/sample_reactions.yaml
```

## 📁 Project Structure

```
chem_lab/
├── main.py                    # CLI entry point
├── input_handler.py          # SMILES/XYZ → ASE atoms conversion
├── product_predictor.py      # LLM/RDKit product prediction
├── molecule_optimizer.py     # DFT geometry optimization
├── reaction_feasibility.py   # Thermodynamic feasibility checks
├── reaction_pathway.py       # NEB and transition state search
├── thermo_kinetics.py        # Gibbs energy and rate calculations
├── network_model.py          # Reaction network graphs
├── visualizer.py             # Energy plots and structure visualization
├── examples/                 # Sample reactions and test cases
└── requirements.txt          # Dependencies
```

## 🔧 Core Modules

### Input Handler
Converts molecular representations (SMILES, XYZ) to ASE atoms objects with validation.

### Product Predictor
- Primary: LLM-based prediction using OpenAI/Perplexity APIs
- Fallback: RDKit with SMARTS pattern matching
- Returns products in SMILES format

### Reaction Pathway
- NEB calculations for reaction pathways
- Transition state optimization
- Activation barrier computation

### Thermodynamics & Kinetics
- Gibbs free energy calculations
- Arrhenius rate constants
- Temperature and pressure effects

## 🧪 Example Usage

```python
from chem_lab import ChemLab

# Initialize the lab
lab = ChemLab()

# Define reactants
reactants = ["CCO", "O=O"]  # Ethanol + Oxygen

# Predict products
products = lab.predict_products(reactants)
print(f"Predicted products: {products}")

# Check feasibility
feasible = lab.check_feasibility(reactants, products)
print(f"Reaction feasible: {feasible}")

# Calculate reaction pathway
pathway = lab.calculate_pathway(reactants, products, temperature=298.15)

# Visualize results
lab.visualize_pathway(pathway)
```

## ⚙️ Configuration

Customize simulation parameters:

- **Temperature**: 200-2000 K
- **Pressure**: 0.1-100 atm  
- **Solvent**: Vacuum, water, organic solvents
- **Catalyst**: Optional catalyst molecules
- **DFT Method**: PBE, B3LYP, M06-2X, etc.
- **Basis Set**: STO-3G, 6-31G*, def2-TZVP, etc.

## 🔬 Supported Calculations

- **Geometry Optimization**: DFT-level structure optimization
- **Single Point Energy**: High-accuracy energy calculations
- **Frequency Analysis**: Vibrational frequencies and thermochemistry
- **NEB Calculations**: Reaction pathway optimization
- **Transition State Search**: Saddle point optimization
- **IRC Calculations**: Intrinsic reaction coordinate

## 📊 Output

The simulation generates:
- Energy diagrams with reaction coordinates
- 3D molecular structures at key points
- Thermodynamic and kinetic parameters
- Reaction network graphs
- Detailed calculation logs

## 🛠️ Dependencies

- **ASE**: Atomic simulation environment
- **PySCF**: Quantum chemistry calculations
- **RDKit**: Cheminformatics and molecule handling
- **OpenAI/Perplexity**: LLM-based predictions
- **NetworkX**: Graph analysis
- **Matplotlib/Plotly**: Visualization

## 📝 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

Contributions welcome! Please read CONTRIBUTING.md for guidelines.

## 📞 Support

For issues and questions, please open a GitHub issue or contact the development team.
