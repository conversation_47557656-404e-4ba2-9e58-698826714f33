Requirement already satisfied: jupyter in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (1.1.1)
Requirement already satisfied: ipywidgets in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (7.8.5)
Collecting py3Dmol
  Using cached py3dmol-2.5.1-py2.py3-none-any.whl.metadata (2.1 kB)
Requirement already satisfied: joblib in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (1.4.2)
Requirement already satisfied: notebook in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyter) (7.3.2)
Requirement already satisfied: jupyter-console in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyter) (6.6.3)
Requirement already satisfied: nbconvert in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyter) (7.16.6)
Requirement already satisfied: ipykernel in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyter) (6.29.5)
Requirement already satisfied: jupyterlab in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyter) (4.3.5)
Requirement already satisfied: comm>=0.1.3 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from ipywidgets) (0.2.2)
Requirement already satisfied: ipython-genutils~=0.2.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from ipywidgets) (0.2.0)
Requirement already satisfied: traitlets>=4.3.1 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from ipywidgets) (5.14.3)
Requirement already satisfied: widgetsnbextension~=3.6.10 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from ipywidgets) (3.6.10)
Requirement already satisfied: ipython>=4.0.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from ipywidgets) (9.0.1)
Requirement already satisfied: jupyterlab-widgets<3,>=1.0.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from ipywidgets) (1.1.11)
Requirement already satisfied: decorator in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from ipython>=4.0.0->ipywidgets) (5.2.1)
Requirement already satisfied: ipython-pygments-lexers in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from ipython>=4.0.0->ipywidgets) (1.1.1)
Requirement already satisfied: jedi>=0.16 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from ipython>=4.0.0->ipywidgets) (0.19.2)
Requirement already satisfied: matplotlib-inline in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from ipython>=4.0.0->ipywidgets) (0.1.7)
Requirement already satisfied: pexpect>4.3 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from ipython>=4.0.0->ipywidgets) (4.9.0)
Requirement already satisfied: prompt_toolkit<3.1.0,>=3.0.41 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from ipython>=4.0.0->ipywidgets) (3.0.50)
Requirement already satisfied: pygments>=2.4.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from ipython>=4.0.0->ipywidgets) (2.19.1)
Requirement already satisfied: stack_data in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from ipython>=4.0.0->ipywidgets) (0.6.3)
Requirement already satisfied: jupyter-server<3,>=2.4.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from notebook->jupyter) (2.15.0)
Requirement already satisfied: jupyterlab-server<3,>=2.27.1 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from notebook->jupyter) (2.27.3)
Requirement already satisfied: notebook-shim<0.3,>=0.2 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from notebook->jupyter) (0.2.4)
Requirement already satisfied: tornado>=6.2.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from notebook->jupyter) (6.4.2)
Requirement already satisfied: async-lru>=1.0.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyterlab->jupyter) (2.0.4)
Requirement already satisfied: httpx>=0.25.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyterlab->jupyter) (0.28.1)
Requirement already satisfied: jinja2>=3.0.3 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyterlab->jupyter) (3.1.5)
Requirement already satisfied: jupyter-core in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyterlab->jupyter) (5.7.2)
Requirement already satisfied: jupyter-lsp>=2.0.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyterlab->jupyter) (2.2.5)
Requirement already satisfied: packaging in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyterlab->jupyter) (24.2)
Requirement already satisfied: setuptools>=40.8.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyterlab->jupyter) (78.1.0)
Requirement already satisfied: appnope in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from ipykernel->jupyter) (0.1.4)
Requirement already satisfied: debugpy>=1.6.5 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from ipykernel->jupyter) (1.8.13)
Requirement already satisfied: jupyter-client>=6.1.12 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from ipykernel->jupyter) (8.6.3)
Requirement already satisfied: nest-asyncio in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from ipykernel->jupyter) (1.6.0)
Requirement already satisfied: psutil in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from ipykernel->jupyter) (7.0.0)
Requirement already satisfied: pyzmq>=24 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from ipykernel->jupyter) (26.2.1)
Requirement already satisfied: beautifulsoup4 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from nbconvert->jupyter) (4.9.0)
Requirement already satisfied: bleach!=5.0.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from bleach[css]!=5.0.0->nbconvert->jupyter) (6.2.0)
Requirement already satisfied: defusedxml in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from nbconvert->jupyter) (0.7.1)
Requirement already satisfied: jupyterlab-pygments in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from nbconvert->jupyter) (0.3.0)
Requirement already satisfied: markupsafe>=2.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from nbconvert->jupyter) (3.0.2)
Requirement already satisfied: mistune<4,>=2.0.3 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from nbconvert->jupyter) (3.1.2)
Requirement already satisfied: nbclient>=0.5.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from nbconvert->jupyter) (0.10.2)
Requirement already satisfied: nbformat>=5.7 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from nbconvert->jupyter) (5.10.4)
Requirement already satisfied: pandocfilters>=1.4.1 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from nbconvert->jupyter) (1.5.1)
Requirement already satisfied: webencodings in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from bleach!=5.0.0->bleach[css]!=5.0.0->nbconvert->jupyter) (0.5.1)
Requirement already satisfied: tinycss2<1.5,>=1.1.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from bleach[css]!=5.0.0->nbconvert->jupyter) (1.4.0)
Requirement already satisfied: anyio in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from httpx>=0.25.0->jupyterlab->jupyter) (4.8.0)
Requirement already satisfied: certifi in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from httpx>=0.25.0->jupyterlab->jupyter) (2025.1.31)
Requirement already satisfied: httpcore==1.* in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from httpx>=0.25.0->jupyterlab->jupyter) (1.0.7)
Requirement already satisfied: idna in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from httpx>=0.25.0->jupyterlab->jupyter) (3.10)
Requirement already satisfied: h11<0.15,>=0.13 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from httpcore==1.*->httpx>=0.25.0->jupyterlab->jupyter) (0.14.0)
Requirement already satisfied: parso<0.9.0,>=0.8.4 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jedi>=0.16->ipython>=4.0.0->ipywidgets) (0.8.4)
Requirement already satisfied: python-dateutil>=2.8.2 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyter-client>=6.1.12->ipykernel->jupyter) (2.9.0.post0)
Requirement already satisfied: platformdirs>=2.5 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyter-core->jupyterlab->jupyter) (4.3.6)
Requirement already satisfied: argon2-cffi>=21.1 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyter-server<3,>=2.4.0->notebook->jupyter) (23.1.0)
Requirement already satisfied: jupyter-events>=0.11.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyter-server<3,>=2.4.0->notebook->jupyter) (0.12.0)
Requirement already satisfied: jupyter-server-terminals>=0.4.4 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyter-server<3,>=2.4.0->notebook->jupyter) (0.5.3)
Requirement already satisfied: overrides>=5.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyter-server<3,>=2.4.0->notebook->jupyter) (7.7.0)
Requirement already satisfied: prometheus-client>=0.9 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyter-server<3,>=2.4.0->notebook->jupyter) (0.21.1)
Requirement already satisfied: send2trash>=1.8.2 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyter-server<3,>=2.4.0->notebook->jupyter) (1.8.3)
Requirement already satisfied: terminado>=0.8.3 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyter-server<3,>=2.4.0->notebook->jupyter) (0.18.1)
Requirement already satisfied: websocket-client>=1.7 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyter-server<3,>=2.4.0->notebook->jupyter) (1.8.0)
Requirement already satisfied: babel>=2.10 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyterlab-server<3,>=2.27.1->notebook->jupyter) (2.17.0)
Requirement already satisfied: json5>=0.9.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyterlab-server<3,>=2.27.1->notebook->jupyter) (0.10.0)
Requirement already satisfied: jsonschema>=4.18.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyterlab-server<3,>=2.27.1->notebook->jupyter) (4.23.0)
Requirement already satisfied: requests>=2.31 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyterlab-server<3,>=2.27.1->notebook->jupyter) (2.32.4)
Requirement already satisfied: fastjsonschema>=2.15 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from nbformat>=5.7->nbconvert->jupyter) (2.21.1)
Requirement already satisfied: ptyprocess>=0.5 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from pexpect>4.3->ipython>=4.0.0->ipywidgets) (0.7.0)
Requirement already satisfied: wcwidth in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from prompt_toolkit<3.1.0,>=3.0.41->ipython>=4.0.0->ipywidgets) (0.2.13)
Requirement already satisfied: soupsieve>1.2 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from beautifulsoup4->nbconvert->jupyter) (2.6)
Requirement already satisfied: executing>=1.2.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from stack_data->ipython>=4.0.0->ipywidgets) (2.2.0)
Requirement already satisfied: asttokens>=2.1.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from stack_data->ipython>=4.0.0->ipywidgets) (3.0.0)
Requirement already satisfied: pure-eval in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from stack_data->ipython>=4.0.0->ipywidgets) (0.2.3)
Requirement already satisfied: sniffio>=1.1 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from anyio->httpx>=0.25.0->jupyterlab->jupyter) (1.3.1)
Requirement already satisfied: argon2-cffi-bindings in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from argon2-cffi>=21.1->jupyter-server<3,>=2.4.0->notebook->jupyter) (21.2.0)
Requirement already satisfied: attrs>=22.2.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jsonschema>=4.18.0->jupyterlab-server<3,>=2.27.1->notebook->jupyter) (25.1.0)
Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jsonschema>=4.18.0->jupyterlab-server<3,>=2.27.1->notebook->jupyter) (2024.10.1)
Requirement already satisfied: referencing>=0.28.4 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jsonschema>=4.18.0->jupyterlab-server<3,>=2.27.1->notebook->jupyter) (0.36.2)
Requirement already satisfied: rpds-py>=0.7.1 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jsonschema>=4.18.0->jupyterlab-server<3,>=2.27.1->notebook->jupyter) (0.23.1)
Requirement already satisfied: python-json-logger>=2.0.4 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->notebook->jupyter) (3.2.1)
Requirement already satisfied: pyyaml>=5.3 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->notebook->jupyter) (6.0.2)
Requirement already satisfied: rfc3339-validator in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->notebook->jupyter) (0.1.4)
Requirement already satisfied: rfc3986-validator>=0.1.1 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->notebook->jupyter) (0.1.1)
Requirement already satisfied: six>=1.5 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from python-dateutil>=2.8.2->jupyter-client>=6.1.12->ipykernel->jupyter) (1.17.0)
Requirement already satisfied: charset_normalizer<4,>=2 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from requests>=2.31->jupyterlab-server<3,>=2.27.1->notebook->jupyter) (3.4.1)
Requirement already satisfied: urllib3<3,>=1.21.1 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from requests>=2.31->jupyterlab-server<3,>=2.27.1->notebook->jupyter) (2.3.0)
Requirement already satisfied: fqdn in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jsonschema[format-nongpl]>=4.18.0->jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->notebook->jupyter) (1.5.1)
Requirement already satisfied: isoduration in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jsonschema[format-nongpl]>=4.18.0->jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->notebook->jupyter) (20.11.0)
Requirement already satisfied: jsonpointer>1.13 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jsonschema[format-nongpl]>=4.18.0->jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->notebook->jupyter) (3.0.0)
Requirement already satisfied: uri-template in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jsonschema[format-nongpl]>=4.18.0->jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->notebook->jupyter) (1.3.0)
Requirement already satisfied: webcolors>=24.6.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from jsonschema[format-nongpl]>=4.18.0->jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->notebook->jupyter) (24.11.1)
Requirement already satisfied: cffi>=1.0.1 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from argon2-cffi-bindings->argon2-cffi>=21.1->jupyter-server<3,>=2.4.0->notebook->jupyter) (1.17.1)
Requirement already satisfied: pycparser in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from cffi>=1.0.1->argon2-cffi-bindings->argon2-cffi>=21.1->jupyter-server<3,>=2.4.0->notebook->jupyter) (2.22)
Requirement already satisfied: arrow>=0.15.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from isoduration->jsonschema[format-nongpl]>=4.18.0->jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->notebook->jupyter) (1.3.0)
Requirement already satisfied: types-python-dateutil>=2.8.10 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from arrow>=0.15.0->isoduration->jsonschema[format-nongpl]>=4.18.0->jupyter-events>=0.11.0->jupyter-server<3,>=2.4.0->notebook->jupyter) (2.9.0.20241206)
Using cached py3dmol-2.5.1-py2.py3-none-any.whl (7.2 kB)
Installing collected packages: py3Dmol
Successfully installed py3Dmol-2.5.1
