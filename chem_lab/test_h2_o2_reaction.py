#!/usr/bin/env python3
"""
Test script to debug H2 + O2 reaction prediction and feasibility
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from product_predictor import ProductPredictor
from reaction_feasibility import ReactionFeasibility<PERSON>hecker
from input_handler import Input<PERSON><PERSON><PERSON>

def test_product_prediction():
    """Test product prediction for H2 + O2 reaction"""
    print("🧪 Testing H2 + O2 Product Prediction")
    print("=" * 50)
    
    predictor = ProductPredictor()
    reactants = ['[H][H]', 'O=O']  # H2 and O2
    
    print(f"Reactants: {reactants}")
    
    # Test with different methods
    print("\n1. Testing with LLM disabled (RDKit + Heuristics only):")
    result = predictor.predict_products(reactants, temperature=600.0, use_llm=False)
    print(f"   Products: {result.products}")
    print(f"   Confidence: {result.confidence}")
    print(f"   Method: {result.method}")
    print(f"   Reaction type: {result.reaction_type}")
    print(f"   Reasoning: {result.reasoning}")
    
    # Test heuristics directly
    print("\n2. Testing heuristics directly:")
    heuristic_result = predictor._predict_with_heuristics(reactants, 600.0)
    print(f"   Products: {heuristic_result.products}")
    print(f"   Confidence: {heuristic_result.confidence}")
    print(f"   Method: {heuristic_result.method}")
    print(f"   Reasoning: {heuristic_result.reasoning}")
    
    # Test RDKit directly
    print("\n3. Testing RDKit directly:")
    try:
        rdkit_result = predictor._predict_with_rdkit(reactants, 600.0)
        print(f"   Products: {rdkit_result.products}")
        print(f"   Confidence: {rdkit_result.confidence}")
        print(f"   Method: {rdkit_result.method}")
        print(f"   Reaction type: {rdkit_result.reaction_type}")
    except Exception as e:
        print(f"   RDKit failed: {e}")
    
    return result

def test_correct_stoichiometry():
    """Test with correct stoichiometry: 2H2 + O2 -> 2H2O"""
    print("\n🧪 Testing Correct Stoichiometry: 2H2 + O2")
    print("=" * 50)
    
    predictor = ProductPredictor()
    reactants = ['[H][H]', '[H][H]', 'O=O']  # 2H2 + O2
    
    print(f"Reactants: {reactants}")
    
    result = predictor.predict_products(reactants, temperature=600.0, use_llm=False)
    print(f"Products: {result.products}")
    print(f"Confidence: {result.confidence}")
    print(f"Method: {result.method}")
    print(f"Reaction type: {result.reaction_type}")
    
    return result

def test_manual_water_formation():
    """Test feasibility with manually specified water products"""
    print("\n🧪 Testing Manual Water Formation")
    print("=" * 50)
    
    input_handler = InputHandler()
    feasibility_checker = ReactionFeasibilityChecker()
    
    # Parse reactants
    h2_atoms = input_handler.parse_molecule('[H][H]')
    o2_atoms = input_handler.parse_molecule('O=O')
    
    # Parse products (water)
    h2o_atoms = input_handler.parse_molecule('O')  # Water as 'O' in SMILES
    
    print("Reactants:")
    print(f"  H2: {len(h2_atoms)} atoms")
    print(f"  O2: {len(o2_atoms)} atoms")
    print("Products:")
    print(f"  H2O: {len(h2o_atoms)} atoms")
    
    # Test feasibility
    try:
        is_feasible, details = feasibility_checker.check_feasibility(
            reactants=[h2_atoms, o2_atoms],
            products=[h2o_atoms, h2o_atoms],  # 2 H2O molecules
            temperature=600.0,
            pressure=1.0
        )
        
        print(f"\nFeasibility: {is_feasible}")
        print(f"Details: {details}")
        
    except Exception as e:
        print(f"Feasibility check failed: {e}")
        import traceback
        traceback.print_exc()

def create_improved_predictor():
    """Create an improved product predictor with better H2 + O2 handling"""
    print("\n🔧 Creating Improved Product Predictor")
    print("=" * 50)
    
    # We'll modify the heuristics to handle H2 + O2 -> H2O correctly
    predictor = ProductPredictor()
    
    # Override the heuristics method
    original_heuristics = predictor._predict_with_heuristics
    
    def improved_heuristics(reactants, temperature):
        """Improved heuristics with H2 + O2 -> H2O support"""
        
        # Check for H2 + O2 reaction
        h2_count = sum(1 for r in reactants if r in ['[H][H]', 'H'])
        o2_count = sum(1 for r in reactants if r in ['O=O', '[O][O]'])
        
        if h2_count >= 1 and o2_count >= 1:
            # H2 + O2 -> H2O reaction
            from product_predictor import PredictionResult
            
            # Determine number of water molecules based on stoichiometry
            # 2H2 + O2 -> 2H2O, so min(h2_count, 2*o2_count) water molecules
            water_count = min(h2_count, 2 * o2_count)
            
            products = ['O'] * water_count  # Water as 'O' in SMILES
            
            return PredictionResult(
                products=products,
                confidence=0.8,  # High confidence for this well-known reaction
                method="Improved Heuristics",
                reaction_type="combustion",
                reasoning=f"H2 + O2 combustion forming {water_count} H2O molecules"
            )
        
        # Fall back to original heuristics for other reactions
        return original_heuristics(reactants, temperature)
    
    # Replace the method
    predictor._predict_with_heuristics = improved_heuristics
    
    return predictor

def test_improved_predictor():
    """Test the improved predictor"""
    print("\n🧪 Testing Improved Product Predictor")
    print("=" * 50)
    
    predictor = create_improved_predictor()
    
    # Test H2 + O2
    reactants1 = ['[H][H]', 'O=O']
    result1 = predictor.predict_products(reactants1, temperature=600.0, use_llm=False)
    print(f"H2 + O2 -> {result1.products} (confidence: {result1.confidence})")
    
    # Test 2H2 + O2
    reactants2 = ['[H][H]', '[H][H]', 'O=O']
    result2 = predictor.predict_products(reactants2, temperature=600.0, use_llm=False)
    print(f"2H2 + O2 -> {result2.products} (confidence: {result2.confidence})")
    
    return predictor

if __name__ == "__main__":
    print("🔬 H2 + O2 Reaction Debugging")
    print("=" * 60)
    
    # Run tests
    test_product_prediction()
    test_correct_stoichiometry()
    test_manual_water_formation()
    
    # Test improved predictor
    improved_predictor = test_improved_predictor()
    
    print("\n✅ Testing complete!")
