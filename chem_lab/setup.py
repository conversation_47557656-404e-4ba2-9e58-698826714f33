#!/usr/bin/env python3
"""
Setup script for Virtual Chemistry Simulation Lab

Handles installation, dependency checking, and environment setup.

Author: AI Chemistry Lab
License: MIT
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python {version.major}.{version.minor} detected")
        print("⚠️  Python 3.8 or higher is required")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True


def install_dependencies():
    """Install required dependencies."""
    print("\n📦 Installing dependencies...")
    
    try:
        # Upgrade pip first
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'])
        
        # Install from requirements.txt
        requirements_file = Path(__file__).parent / 'requirements.txt'
        if requirements_file.exists():
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)])
            print("✅ Dependencies installed successfully")
            return True
        else:
            print("❌ requirements.txt not found")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def setup_environment():
    """Set up environment variables and directories."""
    print("\n🔧 Setting up environment...")
    
    # Create necessary directories
    directories = ['results', 'visualizations', 'logs']
    
    for directory in directories:
        dir_path = Path(directory)
        dir_path.mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    # Check for optional API keys
    api_keys = {
        'OPENAI_API_KEY': 'OpenAI API (for LLM product prediction)',
        'PERPLEXITY_API_KEY': 'Perplexity API (for LLM product prediction)'
    }
    
    print("\n🔑 Checking API keys (optional):")
    for key, description in api_keys.items():
        if os.getenv(key):
            print(f"✅ {key} found")
        else:
            print(f"⚠️  {key} not set - {description} will be disabled")
    
    return True


def run_tests():
    """Run installation tests."""
    print("\n🧪 Running installation tests...")
    
    try:
        # Run the test script
        test_script = Path(__file__).parent / 'test_installation.py'
        if test_script.exists():
            result = subprocess.run([sys.executable, str(test_script)], 
                                  capture_output=True, text=True)
            
            print(result.stdout)
            if result.stderr:
                print("Warnings/Errors:")
                print(result.stderr)
            
            return result.returncode == 0
        else:
            print("❌ Test script not found")
            return False
            
    except Exception as e:
        print(f"❌ Failed to run tests: {e}")
        return False


def create_example_config():
    """Create example configuration files."""
    print("\n📝 Creating example configuration...")
    
    # Create a simple example config
    example_config = """# Example configuration for Virtual Chemistry Simulation Lab
# Usage: python main.py --input-file example_config.yaml

# Simple hydrogen dissociation example
reactants:
  - "[H][H]"  # Hydrogen molecule

# Simulation parameters
temperature: 298.15  # K
pressure: 1.0        # atm
method: "PBE"        # DFT method
basis: "STO-3G"      # Basis set
neb_images: 5        # Number of NEB images
output_dir: "results/example"

# Optional settings
use_llm: false       # Disable LLM for this example
visualize_results: true
save_output: true
"""
    
    config_file = Path('example_config.yaml')
    with open(config_file, 'w') as f:
        f.write(example_config)
    
    print(f"✅ Created {config_file}")
    
    return True


def print_usage_instructions():
    """Print usage instructions."""
    print("\n" + "="*60)
    print("🎉 Setup completed successfully!")
    print("="*60)
    
    print("\n📚 Quick Start Guide:")
    print("1. Test the installation:")
    print("   python test_installation.py")
    
    print("\n2. Run the main CLI:")
    print("   python main.py --help")
    
    print("\n3. Try a simple example:")
    print("   python main.py --input-file example_config.yaml")
    
    print("\n4. Interactive mode:")
    print("   python main.py --interactive")
    
    print("\n5. Run sample reactions:")
    print("   python examples/sample_reactions.py --list")
    print("   python examples/sample_reactions.py --reaction h2_dissociation")
    
    print("\n6. Custom reaction:")
    print("   python main.py -r 'CCO' -r 'O=O' --temperature 450")
    
    print("\n📖 Documentation:")
    print("   - README.md: Complete documentation")
    print("   - examples/: Sample reactions and configurations")
    print("   - requirements.txt: List of dependencies")
    
    print("\n🔧 Configuration:")
    print("   - Set OPENAI_API_KEY for LLM product prediction")
    print("   - Set PERPLEXITY_API_KEY for alternative LLM")
    print("   - Modify example_config.yaml for custom settings")
    
    print("\n⚠️  Important Notes:")
    print("   - Some calculations may take time for large molecules")
    print("   - Start with simple molecules (H2, H2O, CH4)")
    print("   - Use STO-3G basis set for faster calculations")
    print("   - Check test_installation.py output for any issues")


def main():
    """Main setup function."""
    print("🧪 Virtual Chemistry Simulation Lab - Setup")
    print("="*50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Setup failed during dependency installation")
        sys.exit(1)
    
    # Set up environment
    if not setup_environment():
        print("\n❌ Setup failed during environment setup")
        sys.exit(1)
    
    # Create example config
    if not create_example_config():
        print("\n❌ Setup failed during example creation")
        sys.exit(1)
    
    # Run tests
    if not run_tests():
        print("\n⚠️  Setup completed but some tests failed")
        print("The system may still work, but check the test output above")
    
    # Print usage instructions
    print_usage_instructions()
    
    print("\n✅ Setup completed!")


if __name__ == "__main__":
    main()
