"""
Enhanced Result Saver

Implements improved output folder structure and comprehensive result reporting.
Creates organized directories for different types of results and generates
detailed summary reports.

Author: AI Chemistry Lab
License: MIT
"""

import os
import json
import shutil
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path

from result_validator import ValidatedPrediction, PredictionSource, ValidationStatus


class EnhancedResultSaver:
    """
    Enhanced result saver with organized folder structure and comprehensive reporting.
    
    Output structure:
    results/
    ├── final_report.json          # Main summary report
    ├── ML_outputs/               # ML model predictions
    │   ├── feasibility_result.json
    │   ├── product_prediction.json
    │   └── model_confidence.json
    ├── NEB_failed/              # Failed DFT calculations
    │   ├── pathway_attempt.json
    │   ├── convergence_log.txt
    │   └── error_analysis.json
    ├── Validated_outputs/       # Final validated results
    │   ├── validated_prediction.json
    │   ├── confidence_analysis.json
    │   └── model_agreement.json
    └── raw_data/               # Raw calculation outputs
        ├── geometries/
        ├── energies/
        └── logs/
    """
    
    def __init__(self, base_output_dir: str):
        """Initialize the enhanced result saver."""
        self.base_dir = Path(base_output_dir)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create directory structure
        self.directories = {
            'base': self.base_dir,
            'ml_outputs': self.base_dir / 'ML_outputs',
            'neb_failed': self.base_dir / 'NEB_failed',
            'validated': self.base_dir / 'Validated_outputs',
            'raw_data': self.base_dir / 'raw_data',
            'geometries': self.base_dir / 'raw_data' / 'geometries',
            'energies': self.base_dir / 'raw_data' / 'energies',
            'logs': self.base_dir / 'raw_data' / 'logs'
        }
        
        self._create_directories()
    
    def _create_directories(self):
        """Create the organized directory structure."""
        for dir_path in self.directories.values():
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def save_comprehensive_results(self, 
                                 validated_prediction: ValidatedPrediction,
                                 raw_results: Dict[str, Any],
                                 reactants: List[str],
                                 conditions: Dict[str, Any]) -> str:
        """
        Save comprehensive results with organized structure.
        
        Args:
            validated_prediction: Final validated prediction
            raw_results: Raw results from all methods
            reactants: Input reactant SMILES
            conditions: Reaction conditions
            
        Returns:
            Path to the main report file
        """
        
        # 1. Save final report
        report_path = self._save_final_report(validated_prediction, reactants, conditions)
        
        # 2. Save ML outputs
        self._save_ml_outputs(raw_results.get('ml_results', {}))
        
        # 3. Save NEB failure analysis (if applicable)
        if validated_prediction.prediction_source != PredictionSource.DFT_NEB:
            self._save_neb_failure_analysis(raw_results.get('pathway_result'), 
                                          raw_results.get('thermo_data'))
        
        # 4. Save validated outputs
        self._save_validated_outputs(validated_prediction)
        
        # 5. Save raw data
        self._save_raw_data(raw_results)
        
        return str(report_path)
    
    def _save_final_report(self, validated_prediction: ValidatedPrediction,
                          reactants: List[str], conditions: Dict[str, Any]) -> Path:
        """Create and save the main summary report."""
        
        report = {
            "metadata": {
                "timestamp": self.timestamp,
                "reactants": reactants,
                "conditions": conditions,
                "analysis_version": "2.0"
            },
            "final_prediction": {
                "products": validated_prediction.products,
                "activation_energy_kcal_mol": validated_prediction.activation_energy,
                "reaction_energy_kcal_mol": validated_prediction.reaction_energy,
                "delta_g_kcal_mol": validated_prediction.delta_g,
                "delta_h_kcal_mol": validated_prediction.delta_h,
                "is_feasible": validated_prediction.is_feasible,
                "confidence_score": validated_prediction.confidence_score
            },
            "prediction_metadata": {
                "source": validated_prediction.prediction_source.value,
                "validation_status": validated_prediction.validation_status.value,
                "model_agreement": validated_prediction.model_agreement,
                "convergence_status": validated_prediction.convergence_status,
                "uncertainty_estimates": validated_prediction.uncertainty_estimates
            },
            "validation_summary": {
                "messages": validated_prediction.validation_messages,
                "trusted_models": self._get_trusted_models(validated_prediction),
                "discarded_results": self._get_discarded_results(validated_prediction),
                "confidence_breakdown": self._analyze_confidence(validated_prediction)
            },
            "recommendations": {
                "reliability": self._assess_reliability(validated_prediction),
                "next_steps": self._suggest_next_steps(validated_prediction),
                "experimental_guidance": self._provide_experimental_guidance(validated_prediction)
            }
        }
        
        report_path = self.directories['base'] / 'final_report.json'
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        return report_path
    
    def _save_ml_outputs(self, ml_results: Dict[str, Any]):
        """Save ML model outputs to organized structure."""
        
        if not ml_results:
            return
        
        # Save individual ML components
        components = ['feasibility_result', 'product_prediction', 'yield_prediction']
        for component in components:
            if component in ml_results:
                file_path = self.directories['ml_outputs'] / f'{component}.json'
                with open(file_path, 'w') as f:
                    json.dump(ml_results[component], f, indent=2)
        
        # Save model confidence analysis
        confidence_analysis = {
            "individual_confidences": {
                model: result.get('confidence', 0) 
                for model, result in ml_results.items() 
                if isinstance(result, dict)
            },
            "ensemble_confidence": ml_results.get('ensemble_confidence', 0),
            "confidence_factors": ml_results.get('confidence_factors', {}),
            "model_agreement_score": ml_results.get('agreement_score', 0)
        }
        
        confidence_path = self.directories['ml_outputs'] / 'model_confidence.json'
        with open(confidence_path, 'w') as f:
            json.dump(confidence_analysis, f, indent=2)
    
    def _save_neb_failure_analysis(self, pathway_result: Any, thermo_data: Dict[str, Any]):
        """Save analysis of NEB calculation failures."""
        
        failure_analysis = {
            "failure_timestamp": self.timestamp,
            "convergence_status": getattr(pathway_result, 'converged', False) if pathway_result else False,
            "failure_reasons": [],
            "diagnostic_data": {},
            "recommendations": []
        }
        
        if pathway_result:
            # Analyze convergence issues
            if not getattr(pathway_result, 'converged', True):
                failure_analysis["failure_reasons"].append("SCF convergence failure")
                failure_analysis["recommendations"].append("Improve initial geometry guess")
            
            # Check activation energy magnitude
            activation_energy = getattr(pathway_result, 'activation_energy', None)
            if activation_energy and activation_energy * 23.06 > 200:  # Convert eV to kcal/mol
                failure_analysis["failure_reasons"].append("Unrealistic activation energy")
                failure_analysis["diagnostic_data"]["activation_energy_ev"] = activation_energy
                failure_analysis["diagnostic_data"]["activation_energy_kcal"] = activation_energy * 23.06
                failure_analysis["recommendations"].append("Check transition state geometry")
        
        if thermo_data and thermo_data.get('rate_constant') == 0:
            failure_analysis["failure_reasons"].append("Zero rate constant")
            failure_analysis["recommendations"].append("Verify thermodynamic consistency")
        
        # Save failure analysis
        failure_path = self.directories['neb_failed'] / 'error_analysis.json'
        with open(failure_path, 'w') as f:
            json.dump(failure_analysis, f, indent=2)
        
        # Save pathway attempt data if available
        if pathway_result:
            pathway_data = {
                "attempted_calculation": True,
                "converged": getattr(pathway_result, 'converged', False),
                "activation_energy_ev": getattr(pathway_result, 'activation_energy', None),
                "reaction_energy_ev": getattr(pathway_result, 'reaction_energy', None),
                "calculation_method": "NEB",
                "failure_mode": "convergence" if not getattr(pathway_result, 'converged', True) else "energy"
            }
            
            pathway_path = self.directories['neb_failed'] / 'pathway_attempt.json'
            with open(pathway_path, 'w') as f:
                json.dump(pathway_data, f, indent=2)

    def _save_validated_outputs(self, validated_prediction: ValidatedPrediction):
        """Save validated prediction outputs."""

        # Main validated prediction
        validated_data = {
            "prediction": {
                "products": validated_prediction.products,
                "activation_energy": validated_prediction.activation_energy,
                "reaction_energy": validated_prediction.reaction_energy,
                "delta_g": validated_prediction.delta_g,
                "delta_h": validated_prediction.delta_h,
                "is_feasible": validated_prediction.is_feasible
            },
            "metadata": {
                "source": validated_prediction.prediction_source.value,
                "confidence": validated_prediction.confidence_score,
                "validation_status": validated_prediction.validation_status.value,
                "timestamp": self.timestamp
            },
            "quality_metrics": {
                "convergence_status": validated_prediction.convergence_status,
                "uncertainty_estimates": validated_prediction.uncertainty_estimates,
                "validation_messages": validated_prediction.validation_messages
            }
        }

        validated_path = self.directories['validated'] / 'validated_prediction.json'
        with open(validated_path, 'w') as f:
            json.dump(validated_data, f, indent=2)

        # Model agreement analysis
        agreement_analysis = {
            "agreement_scores": validated_prediction.model_agreement,
            "consensus_level": self._calculate_consensus_level(validated_prediction.model_agreement),
            "contributing_models": self._identify_contributing_models(validated_prediction),
            "agreement_factors": self._analyze_agreement_factors(validated_prediction)
        }

        agreement_path = self.directories['validated'] / 'model_agreement.json'
        with open(agreement_path, 'w') as f:
            json.dump(agreement_analysis, f, indent=2)

        # Confidence analysis
        confidence_data = {
            "overall_confidence": validated_prediction.confidence_score,
            "confidence_breakdown": self._analyze_confidence(validated_prediction),
            "uncertainty_sources": validated_prediction.uncertainty_estimates,
            "reliability_assessment": self._assess_reliability(validated_prediction)
        }

        confidence_path = self.directories['validated'] / 'confidence_analysis.json'
        with open(confidence_path, 'w') as f:
            json.dump(confidence_data, f, indent=2)

    def _save_raw_data(self, raw_results: Dict[str, Any]):
        """Save raw calculation data."""

        # Save raw results summary
        raw_summary = {
            "available_results": list(raw_results.keys()),
            "timestamp": self.timestamp,
            "data_sources": {}
        }

        for key, value in raw_results.items():
            if value is not None:
                raw_summary["data_sources"][key] = type(value).__name__

        summary_path = self.directories['raw_data'] / 'raw_summary.json'
        with open(summary_path, 'w') as f:
            json.dump(raw_summary, f, indent=2)

        # Save individual raw results
        for key, value in raw_results.items():
            if value is not None and hasattr(value, '__dict__'):
                try:
                    # Convert object to dictionary if possible
                    if hasattr(value, 'to_dict'):
                        data = value.to_dict()
                    else:
                        data = value.__dict__

                    file_path = self.directories['raw_data'] / f'{key}.json'
                    with open(file_path, 'w') as f:
                        json.dump(data, f, indent=2, default=str)
                except Exception as e:
                    # Save error info if serialization fails
                    error_data = {
                        "error": f"Failed to serialize {key}: {str(e)}",
                        "type": type(value).__name__,
                        "available_attributes": dir(value) if hasattr(value, '__dict__') else []
                    }

                    error_path = self.directories['raw_data'] / f'{key}_error.json'
                    with open(error_path, 'w') as f:
                        json.dump(error_data, f, indent=2)

    def _get_trusted_models(self, validated_prediction: ValidatedPrediction) -> List[str]:
        """Identify which models were trusted in the final prediction."""
        trusted = []

        if validated_prediction.prediction_source == PredictionSource.DFT_NEB:
            trusted.append("DFT/NEB")
        if validated_prediction.prediction_source == PredictionSource.ML_SURROGATE:
            trusted.append("ML Surrogate Models")
        if validated_prediction.prediction_source == PredictionSource.REACTION_NETWORK:
            trusted.append("Reaction Network")
        if validated_prediction.prediction_source == PredictionSource.ENSEMBLE:
            trusted.extend(["ML Surrogate Models", "Reaction Network"])
        if validated_prediction.prediction_source == PredictionSource.FALLBACK:
            trusted.append("ML Fallback")

        return trusted

    def _get_discarded_results(self, validated_prediction: ValidatedPrediction) -> List[str]:
        """Identify which results were discarded and why."""
        discarded = []

        if not validated_prediction.convergence_status.get('dft', True):
            discarded.append("DFT/NEB (convergence failure)")

        if validated_prediction.prediction_source != PredictionSource.DFT_NEB and validated_prediction.dft_result:
            discarded.append("DFT/NEB (unrealistic energies)")

        return discarded

    def _analyze_confidence(self, validated_prediction: ValidatedPrediction) -> Dict[str, Any]:
        """Analyze confidence score breakdown."""
        return {
            "overall_confidence": validated_prediction.confidence_score,
            "confidence_level": "high" if validated_prediction.confidence_score > 0.8 else
                              "medium" if validated_prediction.confidence_score > 0.6 else "low",
            "contributing_factors": {
                "model_agreement": validated_prediction.model_agreement,
                "convergence_quality": validated_prediction.convergence_status,
                "prediction_source": validated_prediction.prediction_source.value
            },
            "uncertainty_factors": validated_prediction.uncertainty_estimates
        }

    def _assess_reliability(self, validated_prediction: ValidatedPrediction) -> str:
        """Assess overall reliability of the prediction."""
        if (validated_prediction.confidence_score > 0.8 and
            validated_prediction.validation_status == ValidationStatus.PASSED):
            return "high"
        elif (validated_prediction.confidence_score > 0.6 and
              validated_prediction.validation_status != ValidationStatus.FAILED):
            return "medium"
        else:
            return "low"

    def _suggest_next_steps(self, validated_prediction: ValidatedPrediction) -> List[str]:
        """Suggest next steps based on prediction quality."""
        suggestions = []

        if validated_prediction.confidence_score < 0.7:
            suggestions.append("Consider additional experimental validation")

        if not validated_prediction.convergence_status.get('dft', True):
            suggestions.append("Retry DFT calculation with improved initial guess")

        if validated_prediction.prediction_source == PredictionSource.FALLBACK:
            suggestions.append("Investigate DFT convergence issues")

        if validated_prediction.activation_energy > 30:
            suggestions.append("Consider alternative reaction conditions")

        return suggestions

    def _provide_experimental_guidance(self, validated_prediction: ValidatedPrediction) -> Dict[str, Any]:
        """Provide experimental guidance based on predictions."""
        guidance = {
            "feasibility": "likely" if validated_prediction.is_feasible else "unlikely",
            "expected_products": validated_prediction.products,
            "reaction_conditions": {}
        }

        if validated_prediction.activation_energy < 20:
            guidance["reaction_conditions"]["temperature"] = "mild heating may be sufficient"
        elif validated_prediction.activation_energy < 40:
            guidance["reaction_conditions"]["temperature"] = "moderate heating recommended"
        else:
            guidance["reaction_conditions"]["temperature"] = "high temperature or catalyst needed"

        if validated_prediction.delta_g < 0:
            guidance["thermodynamics"] = "thermodynamically favorable"
        else:
            guidance["thermodynamics"] = "may require driving force (e.g., product removal)"

        return guidance

    def _calculate_consensus_level(self, model_agreement: Dict[str, bool]) -> str:
        """Calculate consensus level from model agreement."""
        agreement_count = sum(model_agreement.values())
        total_comparisons = len(model_agreement)

        if total_comparisons == 0:
            return "unknown"

        consensus_ratio = agreement_count / total_comparisons

        if consensus_ratio > 0.8:
            return "strong"
        elif consensus_ratio > 0.6:
            return "moderate"
        else:
            return "weak"

    def _identify_contributing_models(self, validated_prediction: ValidatedPrediction) -> List[str]:
        """Identify which models contributed to the final prediction."""
        contributors = []

        if validated_prediction.ml_result:
            contributors.append("ML Surrogate")
        if validated_prediction.dft_result:
            contributors.append("DFT/NEB")
        if validated_prediction.network_result:
            contributors.append("Reaction Network")

        return contributors

    def _analyze_agreement_factors(self, validated_prediction: ValidatedPrediction) -> Dict[str, Any]:
        """Analyze factors contributing to model agreement."""
        factors = {
            "energy_agreement": False,
            "product_agreement": False,
            "feasibility_agreement": False
        }

        # Check if multiple models agree on key predictions
        if validated_prediction.model_agreement.get('ml_network_energy', False):
            factors["energy_agreement"] = True
        if validated_prediction.model_agreement.get('ml_network_products', False):
            factors["product_agreement"] = True
        if validated_prediction.model_agreement.get('all_methods_feasible', False):
            factors["feasibility_agreement"] = True

        return factors
