#!/usr/bin/env python3
"""
Installation Test Script

Tests the basic functionality of all modules to ensure proper installation
and integration of the Virtual Chemistry Simulation Lab.

Author: AI Chemistry Lab
License: MIT
"""

import sys
import warnings
from pathlib import Path

def test_imports():
    """Test that all modules can be imported."""
    print("🔍 Testing module imports...")

    modules = [
        'input_handler',
        'product_predictor',
        'molecule_optimizer',
        'reaction_feasibility',
        'reaction_pathway',
        'thermo_kinetics',
        'network_model',
        'visualizer'
    ]

    failed_imports = []
    warning_imports = []

    for module in modules:
        try:
            # Suppress warnings during import test
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")
            failed_imports.append(module)
        except Exception as e:
            print(f"⚠️  {module}: {e}")
            warning_imports.append(module)

    if failed_imports:
        print(f"\n❌ Failed to import: {failed_imports}")
        return False
    elif warning_imports:
        print(f"\n⚠️ Imported with warnings: {warning_imports}")
        print("These modules may have limited functionality.")
        return True
    else:
        print("\n✅ All modules imported successfully!")
        return True


def test_dependencies():
    """Test that required dependencies are available."""
    print("\n🔍 Testing dependencies...")
    
    dependencies = {
        'ase': 'Atomic Simulation Environment',
        'numpy': 'NumPy',
        'matplotlib': 'Matplotlib (optional)',
        'plotly': 'Plotly (optional)',
        'networkx': 'NetworkX (optional)',
        'rdkit': 'RDKit (optional)',
        'pyscf': 'PySCF (optional)',
        'click': 'Click CLI',
        'yaml': 'PyYAML'
    }
    
    available = []
    missing = []
    
    for module, description in dependencies.items():
        try:
            if module == 'yaml':
                import yaml
            else:
                __import__(module)
            print(f"✅ {description}")
            available.append(module)
        except ImportError:
            print(f"❌ {description} - not available")
            missing.append(module)
    
    print(f"\n📊 Available: {len(available)}/{len(dependencies)} dependencies")
    
    if 'ase' not in available or 'numpy' not in available:
        print("❌ Critical dependencies missing!")
        return False
    
    return True


def test_basic_functionality():
    """Test basic functionality of key modules."""
    print("\n🔍 Testing basic functionality...")

    try:
        # Test input handler
        from input_handler import InputHandler
        handler = InputHandler()

        # Test with simple coordinate string
        water_coords = """O 0.0 0.0 0.0
H 0.757 0.586 0.0
H -0.757 0.586 0.0"""

        atoms = handler.parse_molecule(water_coords)
        validation = handler.validate_molecule(atoms)

        print(f"✅ Input handler: Parsed {atoms.get_chemical_formula()}")
        print(f"   Validation: {validation['is_valid']}")

    except Exception as e:
        print(f"❌ Input handler test failed: {e}")
        return False

    try:
        # Test molecule optimizer with warnings suppressed
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            from molecule_optimizer import MoleculeOptimizer
            optimizer = MoleculeOptimizer(method="PBE", basis="STO-3G")

            # Estimate computational cost
            cost = optimizer.estimate_computational_cost(atoms, "PBE", "STO-3G")
            print(f"✅ Molecule optimizer: Cost estimate {cost['difficulty']}")

    except Exception as e:
        print(f"❌ Molecule optimizer test failed: {e}")
        return False

    try:
        # Test product predictor (without LLM)
        from product_predictor import ProductPredictor
        predictor = ProductPredictor()

        result = predictor.predict_products(['CCO'], use_llm=False)
        print(f"✅ Product predictor: {result.method} method")

    except Exception as e:
        print(f"❌ Product predictor test failed: {e}")
        return False

    try:
        # Test feasibility checker with warnings suppressed
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            from reaction_feasibility import ReactionFeasibilityChecker
            checker = ReactionFeasibilityChecker()

            # Simple thermodynamic calculation
            thermo = checker.calculate_thermodynamics_from_energies(-1.0, -1.5)
            print(f"✅ Feasibility checker: ΔG = {thermo['delta_g']:.3f} eV")

    except Exception as e:
        print(f"❌ Feasibility checker test failed: {e}")
        return False

    try:
        # Test thermo calculator with warnings suppressed
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            from thermo_kinetics import ThermoKineticsCalculator
            thermo_calc = ThermoKineticsCalculator()

            rate_info = thermo_calc.calculate_rate_from_barrier(1.0)
            print(f"✅ Thermo calculator: Rate = {rate_info['rate_constant']:.2e} s⁻¹")

    except Exception as e:
        print(f"❌ Thermo calculator test failed: {e}")
        return False

    print("\n✅ Basic functionality tests passed!")
    return True


def test_visualization():
    """Test visualization capabilities."""
    print("\n🔍 Testing visualization...")
    
    try:
        from visualizer import ReactionVisualizer
        visualizer = ReactionVisualizer()
        print("✅ Visualizer initialized")
        
        # Check available plotting libraries
        try:
            import matplotlib.pyplot as plt
            print("✅ Matplotlib available")
        except ImportError:
            print("❌ Matplotlib not available")
        
        try:
            import plotly
            print("✅ Plotly available")
        except ImportError:
            print("❌ Plotly not available")
        
        return True
        
    except Exception as e:
        print(f"❌ Visualization test failed: {e}")
        return False


def test_examples():
    """Test example functionality."""
    print("\n🔍 Testing examples...")
    
    try:
        from examples.sample_reactions import SampleReactions
        samples = SampleReactions()
        
        reactions = samples.list_reactions()
        print(f"✅ Sample reactions: {len(reactions)} available")
        
        # Test custom reaction creation
        custom = samples.create_custom_reaction(['CCO'], ['C=C', 'O'])
        print(f"✅ Custom reaction: {custom['name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Examples test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🧪 Virtual Chemistry Simulation Lab - Installation Test")
    print("=" * 60)
    
    tests = [
        ("Module Imports", test_imports),
        ("Dependencies", test_dependencies),
        ("Basic Functionality", test_basic_functionality),
        ("Visualization", test_visualization),
        ("Examples", test_examples)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Installation is working correctly.")
        print("\nNext steps:")
        print("1. Try running: python main.py --help")
        print("2. Run a sample reaction: python examples/sample_reactions.py --list")
        print("3. Test with simple molecules: python main.py -r 'CCO' -r 'O=O'")
        return True
    else:
        print("⚠️  Some tests failed. Check the error messages above.")
        print("\nTroubleshooting:")
        print("1. Install missing dependencies: pip install -r requirements.txt")
        print("2. Check Python version (3.8+ recommended)")
        print("3. Verify ASE and NumPy are properly installed")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
