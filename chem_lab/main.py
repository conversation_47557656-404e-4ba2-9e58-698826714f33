#!/usr/bin/env python3
"""
Virtual Chemistry Simulation Lab - Main Entry Point

A comprehensive toolkit for simulating realistic chemical reactions using
quantum chemistry and AI techniques.

Author: AI Chemistry Lab
License: MIT
"""

import click
import sys
import os
import yaml
import json
from pathlib import Path
from typing import List, Dict, Any, Optional

# Import our modules
from input_handler import InputHandler
from product_predictor import ProductPredictor
from molecule_optimizer import MoleculeOptimizer
from reaction_feasibility import ReactionFeasibilityChecker
from reaction_pathway import ReactionPathwayCalculator
from thermo_kinetics import ThermoKineticsCalculator
from network_model import ReactionNetworkModel
from visualizer import ReactionVisualizer

# Import new validation and enhancement modules
from result_validator import ResultValidator, ValidatedPrediction
from enhanced_result_saver import EnhancedResultSaver
from neb_convergence_helper import NEBConvergenceHelper


class ChemLab:
    """Main chemistry simulation lab class."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the chemistry lab with default or custom configuration."""
        self.config = config or self._default_config()
        
        # Initialize modules
        self.input_handler = InputHandler()
        self.product_predictor = ProductPredictor(
            openai_api_key=os.getenv('OPENAI_API_KEY'),
            perplexity_api_key=os.getenv('PERPLEXITY_API_KEY')
        )
        self.optimizer = MoleculeOptimizer(
            method=self.config['dft_method'],
            basis=self.config['basis_set']
        )
        self.feasibility_checker = ReactionFeasibilityChecker()
        self.pathway_calculator = ReactionPathwayCalculator()
        self.thermo_calculator = ThermoKineticsCalculator()
        self.network_model = ReactionNetworkModel()
        self.visualizer = ReactionVisualizer()

        # Initialize new validation and enhancement modules
        self.result_validator = ResultValidator()
        self.neb_helper = NEBConvergenceHelper()
    
    def _default_config(self) -> Dict[str, Any]:
        """Return default configuration parameters."""
        return {
            'temperature': 298.15,  # K
            'pressure': 1.0,        # atm
            'dft_method': 'PBE',
            'basis_set': '6-31G*',
            'solvent': 'vacuum',
            'catalyst': None,
            'neb_images': 8,
            'max_force': 0.05,      # eV/Å
            'convergence_energy': 1e-6,  # eV
            'use_llm': True,
            'fallback_to_rdkit': True,
            'visualize_results': True,
            'save_output': True,
            'output_dir': 'results'
        }
    
    def run_complete_simulation(self, reactants: List[str], **kwargs) -> Dict[str, Any]:
        """
        Run a complete reaction simulation workflow.
        
        Args:
            reactants: List of reactant molecules (SMILES or file paths)
            **kwargs: Additional parameters to override config
            
        Returns:
            Dictionary containing all simulation results
        """
        # Update config with any provided parameters
        config = {**self.config, **kwargs}
        
        print(f"🧪 Starting reaction simulation...")
        print(f"📝 Reactants: {reactants}")
        print(f"🌡️  Temperature: {config['temperature']} K")
        print(f"📊 Pressure: {config['pressure']} atm")
        
        results = {}
        
        try:
            # Step 1: Parse and validate input molecules
            print("\n1️⃣ Parsing input molecules...")
            reactant_atoms = []
            for reactant in reactants:
                atoms = self.input_handler.parse_molecule(reactant)
                reactant_atoms.append(atoms)
            results['reactant_atoms'] = reactant_atoms
            
            # Step 2: Predict reaction products
            print("\n2️⃣ Predicting reaction products...")
            products = self.product_predictor.predict_products(
                reactants,
                temperature=config['temperature'],
                use_llm=config['use_llm']
            )
            results['predicted_products'] = products
            print(f"🎯 Predicted products: {products.products}")

            # Step 3: Optimize molecular geometries
            print("\n3️⃣ Optimizing molecular geometries...")
            optimized_reactants = []
            for atoms in reactant_atoms:
                opt_result = self.optimizer.optimize_geometry(atoms)
                optimized_reactants.append(opt_result.atoms)
            results['optimized_reactants'] = optimized_reactants

            # Step 4: Check reaction feasibility
            print("\n4️⃣ Checking reaction feasibility...")
            feasibility = self.feasibility_checker.check_feasibility(
                optimized_reactants, products.products, config['temperature']
            )
            results['feasibility'] = feasibility
            print(f"✅ Reaction feasible: {feasibility.is_feasible}")

            if not feasibility.is_feasible:
                print("❌ Reaction not feasible under current conditions.")
                return results

            # Step 5: Calculate reaction pathway with improved convergence
            print("\n5️⃣ Calculating reaction pathway...")

            # Generate improved initial guess for NEB
            try:
                print("   🔧 Generating improved initial guess...")
                initial_pathway = self.neb_helper.generate_improved_initial_guess(
                    reactant_smiles=reactants,
                    product_smiles=products.products,
                    n_images=config['neb_images']
                )

                # Get optimized NEB parameters
                neb_params = self.neb_helper.suggest_neb_parameters("esterification")
                print(f"   ⚙️ Using optimized NEB parameters: {neb_params['n_images']} images")

            except Exception as e:
                print(f"   ⚠️ Initial guess generation failed: {e}")
                initial_pathway = None
                neb_params = {'n_images': config['neb_images'], 'max_force': config['max_force']}

            # Calculate pathway with improved settings
            pathway = self.pathway_calculator.calculate_pathway(
                optimized_reactants, products.products,
                n_images=neb_params.get('n_images', config['neb_images']),
                max_force=neb_params.get('force_threshold', config['max_force'])
            )
            results['reaction_pathway'] = pathway

            # Diagnose any convergence issues
            if pathway:
                convergence_diagnosis = self.neb_helper.diagnose_convergence_issues(pathway)
                results['neb_diagnosis'] = convergence_diagnosis

                if convergence_diagnosis['severity'] != 'none':
                    print(f"   ⚠️ NEB convergence issues detected ({convergence_diagnosis['severity']} severity)")
                    for issue in convergence_diagnosis['identified_issues'][:2]:
                        print(f"      • {issue}")
                else:
                    print("   ✅ NEB calculation converged successfully")
            
            # Step 6: Compute thermodynamics and kinetics
            print("\n6️⃣ Computing thermodynamics and kinetics...")
            thermo_data = self.thermo_calculator.calculate_properties(
                pathway, config['temperature'], config['pressure']
            )
            results['thermodynamics'] = thermo_data
            
            # Step 7: Build reaction network
            print("\n7️⃣ Building reaction network...")
            network = self.network_model.build_network(
                optimized_reactants, products.products, pathway
            )
            results['reaction_network'] = network

            # Step 8: Validate and select best prediction
            print("\n8️⃣ Validating results and selecting best prediction...")
            validated_prediction = self.result_validator.validate_and_select_prediction(
                pathway_result=pathway,
                feasibility_result=feasibility,
                product_result=products,
                network_result=network,
                thermo_data=thermo_data.__dict__ if hasattr(thermo_data, '__dict__') else thermo_data
            )
            results['validated_prediction'] = validated_prediction

            # Print validation summary
            print(f"🎯 Final prediction source: {validated_prediction.prediction_source.value}")
            print(f"📊 Confidence score: {validated_prediction.confidence_score:.2f}")
            print(f"✅ Validation status: {validated_prediction.validation_status.value}")

            if validated_prediction.validation_messages:
                print("📝 Validation messages:")
                for msg in validated_prediction.validation_messages[-3:]:  # Show last 3 messages
                    print(f"   • {msg}")

            # Step 9: Visualize results
            if config['visualize_results']:
                print("\n9️⃣ Generating visualizations...")
                self.visualizer.plot_energy_diagram(pathway, thermo_data)
                self.visualizer.plot_reaction_network(network)
                self.visualizer.animate_reaction_pathway(pathway)

            # Step 10: Save enhanced results
            if config['save_output']:
                print("\n🔟 Saving comprehensive results...")
                enhanced_saver = EnhancedResultSaver(config['output_dir'])

                # Prepare raw results for saving
                raw_results = {
                    'pathway_result': pathway,
                    'feasibility_result': feasibility,
                    'product_result': products,
                    'network_result': network,
                    'thermo_data': thermo_data,
                    'ml_results': {
                        'feasibility_result': feasibility.__dict__ if hasattr(feasibility, '__dict__') else feasibility,
                        'product_prediction': products.__dict__ if hasattr(products, '__dict__') else products,
                    }
                }

                # Prepare reaction conditions
                reaction_conditions = {
                    'temperature': config['temperature'],
                    'pressure': config['pressure'],
                    'solvent': config.get('solvent', 'vacuum'),
                    'catalyst': config.get('catalyst', None),
                    'method': config['dft_method'],
                    'basis_set': config['basis_set']
                }

                report_path = enhanced_saver.save_comprehensive_results(
                    validated_prediction=validated_prediction,
                    raw_results=raw_results,
                    reactants=reactants,
                    conditions=reaction_conditions
                )

                print(f"📋 Comprehensive report saved: {report_path}")

                # Also save legacy format for compatibility
                self._save_results(results, config['output_dir'])
            
            print("\n✅ Simulation completed successfully!")
            return results
            
        except Exception as e:
            print(f"\n❌ Simulation failed: {str(e)}")
            results['error'] = str(e)
            return results


    def _save_results(self, results: Dict[str, Any], output_dir: str):
        """Save simulation results to files."""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        # Save JSON summary
        summary = {k: v for k, v in results.items()
                   if k not in ['reactant_atoms', 'optimized_reactants']}

        with open(output_path / 'simulation_results.json', 'w') as f:
            json.dump(summary, f, indent=2, default=str)

        print(f"💾 Results saved to {output_dir}/")


@click.command()
@click.option('--reactants', '-r', multiple=True, required=False,
              help='Reactant molecules (SMILES strings)')
@click.option('--input-file', '-f', type=click.Path(exists=True),
              help='Input file with reaction parameters (YAML/JSON)')
@click.option('--temperature', '-T', default=298.15, type=float,
              help='Temperature in Kelvin (default: 298.15)')
@click.option('--pressure', '-P', default=1.0, type=float,
              help='Pressure in atm (default: 1.0)')
@click.option('--method', default='PBE', 
              help='DFT method (default: PBE)')
@click.option('--basis', default='6-31G*',
              help='Basis set (default: 6-31G*)')
@click.option('--solvent', default='vacuum',
              help='Solvent (default: vacuum)')
@click.option('--interactive', is_flag=True,
              help='Run in interactive mode')
@click.option('--no-llm', is_flag=True,
              help='Disable LLM product prediction')
@click.option('--output-dir', '-o', default='results',
              help='Output directory (default: results)')
def main(reactants, input_file, temperature, pressure, method, basis, 
         solvent, interactive, no_llm, output_dir):
    """
    Virtual Chemistry Simulation Lab
    
    Simulate realistic chemical reactions using quantum chemistry and AI.
    """
    
    print("🧪 Virtual Chemistry Simulation Lab")
    print("=" * 50)
    
    # Initialize lab
    config = {
        'temperature': temperature,
        'pressure': pressure,
        'dft_method': method,
        'basis_set': basis,
        'solvent': solvent,
        'use_llm': not no_llm,
        'output_dir': output_dir,
        'neb_images': 8,  # Default number of NEB images
        'max_force': 0.05,  # Default force tolerance
        'visualize_results': True,
        'save_output': True
    }
    
    lab = ChemLab(config)
    
    if interactive:
        _run_interactive_mode(lab)
    elif input_file:
        _run_from_file(lab, input_file)
    elif reactants:
        results = lab.run_complete_simulation(list(reactants))
        if 'error' not in results:
            print("\n🎉 Simulation completed successfully!")
        else:
            print(f"\n❌ Simulation failed: {results['error']}")
            sys.exit(1)
    else:
        print("❌ Please provide reactants, input file, or use --interactive mode")
        print("Use --help for more information")
        sys.exit(1)


def _run_interactive_mode(lab: ChemLab):
    """Run the lab in interactive mode."""
    print("\n🔬 Interactive Mode")
    print("Enter 'quit' or 'exit' to stop")
    
    while True:
        try:
            reactants_input = input("\nEnter reactants (SMILES, space-separated): ").strip()
            if reactants_input.lower() in ['quit', 'exit']:
                break
                
            reactants = reactants_input.split()
            if len(reactants) < 1:
                print("❌ Please enter at least one reactant")
                continue
                
            results = lab.run_complete_simulation(reactants)
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


def _run_from_file(lab: ChemLab, input_file: str):
    """Run simulation from input file."""
    with open(input_file, 'r') as f:
        if input_file.endswith('.yaml') or input_file.endswith('.yml'):
            data = yaml.safe_load(f)
        else:
            data = json.load(f)
    
    reactants = data.get('reactants', [])
    config_updates = {k: v for k, v in data.items() if k != 'reactants'}
    
    results = lab.run_complete_simulation(reactants, **config_updates)


if __name__ == '__main__':
    main()
