#!/usr/bin/env python3
"""
Test the fixed product predictor for H2 + O2 reaction
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from product_predictor import ProductPredictor

def test_fixed_predictor():
    """Test the fixed product predictor"""
    print("🧪 Testing Fixed Product Predictor")
    print("=" * 50)
    
    predictor = ProductPredictor()
    
    # Test H2 + O2
    print("\n1. Testing H2 + O2:")
    reactants1 = ['[H][H]', 'O=O']
    result1 = predictor.predict_products(reactants1, temperature=600.0, use_llm=False)
    print(f"   Reactants: {reactants1}")
    print(f"   Products: {result1.products}")
    print(f"   Confidence: {result1.confidence}")
    print(f"   Method: {result1.method}")
    print(f"   Reaction type: {result1.reaction_type}")
    
    # Test 2H2 + O2
    print("\n2. Testing 2H2 + O2:")
    reactants2 = ['[H][H]', '[H][H]', 'O=O']
    result2 = predictor.predict_products(reactants2, temperature=600.0, use_llm=False)
    print(f"   Reactants: {reactants2}")
    print(f"   Products: {result2.products}")
    print(f"   Confidence: {result2.confidence}")
    print(f"   Method: {result2.method}")
    print(f"   Reaction type: {result2.reaction_type}")
    
    # Test RDKit specifically
    print("\n3. Testing RDKit method directly:")
    try:
        rdkit_result = predictor._predict_with_rdkit(reactants1, 600.0)
        print(f"   Products: {rdkit_result.products}")
        print(f"   Confidence: {rdkit_result.confidence}")
        print(f"   Method: {rdkit_result.method}")
        print(f"   Reaction type: {rdkit_result.reaction_type}")
    except Exception as e:
        print(f"   RDKit failed: {e}")
    
    return result1

if __name__ == "__main__":
    test_fixed_predictor()
