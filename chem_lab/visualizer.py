"""
Visualization Module

Creates interactive visualizations for reaction pathways, energy diagrams,
molecular structures, and reaction networks.

Author: AI Chemistry Lab
License: MIT
"""

import numpy as np
import warnings
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    from matplotlib.animation import FuncAnimation
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    warnings.warn("Matplotlib not available. Basic plotting disabled.")

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    warnings.warn("Plotly not available. Interactive plotting disabled.")

try:
    from ase.visualize import view
    from ase.io import write
    ASE_VIZ_AVAILABLE = True
except ImportError:
    ASE_VIZ_AVAILABLE = False
    warnings.warn("ASE visualization not available.")

from reaction_pathway import PathwayResult
from thermo_kinetics import ThermoKineticResult
from network_model import ReactionNetwork


class ReactionVisualizer:
    """
    Creates comprehensive visualizations for chemical reaction analysis.
    
    Features:
    - Energy diagrams and reaction coordinates
    - Molecular structure visualization
    - Reaction pathway animations
    - Network graphs
    - Interactive plots
    """
    
    def __init__(self, output_dir: str = "visualizations"):
        """Initialize the visualizer."""
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Plotting parameters
        self.figure_size = (12, 8)
        self.dpi = 300
        self.color_scheme = {
            'reactants': '#2E86AB',
            'products': '#A23B72',
            'transition_state': '#F18F01',
            'intermediates': '#C73E1D',
            'pathway': '#1B1B1E'
        }
    
    def plot_energy_diagram(self, pathway: PathwayResult,
                          thermo_data: Optional[ThermoKineticResult] = None,
                          save_path: Optional[str] = None,
                          interactive: bool = True) -> None:
        """
        Plot energy diagram for reaction pathway.
        
        Args:
            pathway: Calculated reaction pathway
            thermo_data: Optional thermodynamic data
            save_path: Path to save the plot
            interactive: Whether to create interactive plot
        """
        
        print("📊 Creating energy diagram...")
        
        if interactive and PLOTLY_AVAILABLE:
            self._plot_energy_diagram_plotly(pathway, thermo_data, save_path)
        elif MATPLOTLIB_AVAILABLE:
            self._plot_energy_diagram_matplotlib(pathway, thermo_data, save_path)
        else:
            warnings.warn("No plotting library available")
    
    def _plot_energy_diagram_plotly(self, pathway: PathwayResult,
                                   thermo_data: Optional[ThermoKineticResult],
                                   save_path: Optional[str]) -> None:
        """Create interactive energy diagram with Plotly."""
        
        # Prepare data
        x = pathway.reaction_coordinate
        y = pathway.energies - pathway.energies[0]  # Relative to reactants
        
        # Create figure
        fig = go.Figure()
        
        # Add energy profile
        fig.add_trace(go.Scatter(
            x=x, y=y,
            mode='lines+markers',
            name='Energy Profile',
            line=dict(color=self.color_scheme['pathway'], width=3),
            marker=dict(size=8),
            hovertemplate='<b>Reaction Coordinate:</b> %{x:.3f} Å<br>' +
                         '<b>Energy:</b> %{y:.3f} eV<br>' +
                         '<extra></extra>'
        ))
        
        # Highlight special points
        ts_idx = pathway.transition_state_index
        fig.add_trace(go.Scatter(
            x=[x[ts_idx]], y=[y[ts_idx]],
            mode='markers',
            name='Transition State',
            marker=dict(
                size=15,
                color=self.color_scheme['transition_state'],
                symbol='star'
            ),
            hovertemplate='<b>Transition State</b><br>' +
                         f'<b>Activation Energy:</b> {pathway.activation_energy:.3f} eV<br>' +
                         '<extra></extra>'
        ))
        
        # Add reactants and products markers
        fig.add_trace(go.Scatter(
            x=[x[0]], y=[y[0]],
            mode='markers',
            name='Reactants',
            marker=dict(
                size=12,
                color=self.color_scheme['reactants'],
                symbol='circle'
            )
        ))
        
        fig.add_trace(go.Scatter(
            x=[x[-1]], y=[y[-1]],
            mode='markers',
            name='Products',
            marker=dict(
                size=12,
                color=self.color_scheme['products'],
                symbol='circle'
            )
        ))
        
        # Update layout
        fig.update_layout(
            title=f'Reaction Energy Diagram ({pathway.method})',
            xaxis_title='Reaction Coordinate (Å)',
            yaxis_title='Relative Energy (eV)',
            template='plotly_white',
            width=800,
            height=600,
            showlegend=True
        )
        
        # Add annotations
        fig.add_annotation(
            x=x[ts_idx], y=y[ts_idx] + 0.1,
            text=f"Ea = {pathway.activation_energy:.2f} eV",
            showarrow=True,
            arrowhead=2,
            arrowcolor=self.color_scheme['transition_state']
        )
        
        fig.add_annotation(
            x=x[-1], y=y[-1] + 0.1,
            text=f"ΔE = {pathway.reaction_energy:.2f} eV",
            showarrow=True,
            arrowhead=2,
            arrowcolor=self.color_scheme['products']
        )
        
        # Save or show
        if save_path:
            fig.write_html(save_path)
        else:
            fig.show()
    
    def _plot_energy_diagram_matplotlib(self, pathway: PathwayResult,
                                      thermo_data: Optional[ThermoKineticResult],
                                      save_path: Optional[str]) -> None:
        """Create energy diagram with Matplotlib."""
        
        # Prepare data
        x = pathway.reaction_coordinate
        y = pathway.energies - pathway.energies[0]  # Relative to reactants
        
        # Create figure
        fig, ax = plt.subplots(figsize=self.figure_size, dpi=self.dpi)
        
        # Plot energy profile
        ax.plot(x, y, 'o-', color=self.color_scheme['pathway'], 
                linewidth=3, markersize=8, label='Energy Profile')
        
        # Highlight special points
        ts_idx = pathway.transition_state_index
        ax.plot(x[ts_idx], y[ts_idx], '*', 
                color=self.color_scheme['transition_state'], 
                markersize=20, label='Transition State')
        
        ax.plot(x[0], y[0], 'o', 
                color=self.color_scheme['reactants'], 
                markersize=12, label='Reactants')
        
        ax.plot(x[-1], y[-1], 'o', 
                color=self.color_scheme['products'], 
                markersize=12, label='Products')
        
        # Add annotations
        ax.annotate(f'Ea = {pathway.activation_energy:.2f} eV',
                   xy=(x[ts_idx], y[ts_idx]),
                   xytext=(x[ts_idx], y[ts_idx] + 0.3),
                   arrowprops=dict(arrowstyle='->', 
                                 color=self.color_scheme['transition_state']),
                   fontsize=12, ha='center')
        
        ax.annotate(f'ΔE = {pathway.reaction_energy:.2f} eV',
                   xy=(x[-1], y[-1]),
                   xytext=(x[-1], y[-1] + 0.2),
                   arrowprops=dict(arrowstyle='->', 
                                 color=self.color_scheme['products']),
                   fontsize=12, ha='center')
        
        # Formatting
        ax.set_xlabel('Reaction Coordinate (Å)', fontsize=14)
        ax.set_ylabel('Relative Energy (eV)', fontsize=14)
        ax.set_title(f'Reaction Energy Diagram ({pathway.method})', fontsize=16)
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=12)
        
        # Save or show
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        else:
            plt.show()
        
        plt.close()
    
    def plot_reaction_network(self, network: ReactionNetwork,
                            save_path: Optional[str] = None,
                            layout: str = "spring") -> None:
        """
        Plot reaction network graph.
        
        Args:
            network: Reaction network
            save_path: Path to save the plot
            layout: Network layout algorithm
        """
        
        print("🕸️ Creating network visualization...")
        
        if not network.graph:
            warnings.warn("No graph available for visualization")
            return
        
        try:
            import networkx as nx
            
            # Create figure
            fig, ax = plt.subplots(figsize=self.figure_size, dpi=self.dpi)
            
            # Choose layout
            if layout == "spring":
                pos = nx.spring_layout(network.graph, k=2, iterations=50)
            elif layout == "circular":
                pos = nx.circular_layout(network.graph)
            elif layout == "hierarchical":
                pos = nx.nx_agraph.graphviz_layout(network.graph, prog='dot')
            else:
                pos = nx.spring_layout(network.graph)
            
            # Draw nodes
            node_colors = []
            node_sizes = []
            
            for node_id in network.graph.nodes():
                node = network.nodes[node_id]
                node_type = node.properties.get('node_type', 'unknown')
                
                if node_type == 'reactants':
                    node_colors.append(self.color_scheme['reactants'])
                    node_sizes.append(1000)
                elif node_type == 'products':
                    node_colors.append(self.color_scheme['products'])
                    node_sizes.append(1000)
                else:
                    node_colors.append(self.color_scheme['intermediates'])
                    node_sizes.append(600)
            
            nx.draw_networkx_nodes(network.graph, pos, 
                                 node_color=node_colors,
                                 node_size=node_sizes,
                                 alpha=0.8, ax=ax)
            
            # Draw edges
            edge_colors = []
            edge_widths = []
            
            for edge in network.edges:
                # Color by activation energy
                if edge.activation_energy < 1.0:
                    edge_colors.append('green')
                    edge_widths.append(3)
                elif edge.activation_energy < 2.0:
                    edge_colors.append('orange')
                    edge_widths.append(2)
                else:
                    edge_colors.append('red')
                    edge_widths.append(1)
            
            nx.draw_networkx_edges(network.graph, pos,
                                 edge_color=edge_colors,
                                 width=edge_widths,
                                 alpha=0.6, ax=ax)
            
            # Add labels
            labels = {}
            for node_id, node in network.nodes.items():
                labels[node_id] = node.properties.get('node_type', node_id)
            
            nx.draw_networkx_labels(network.graph, pos, labels,
                                  font_size=10, ax=ax)
            
            # Formatting
            ax.set_title('Reaction Network', fontsize=16)
            ax.axis('off')
            
            # Add legend
            legend_elements = [
                plt.Line2D([0], [0], marker='o', color='w', 
                          markerfacecolor=self.color_scheme['reactants'],
                          markersize=15, label='Reactants'),
                plt.Line2D([0], [0], marker='o', color='w',
                          markerfacecolor=self.color_scheme['products'],
                          markersize=15, label='Products'),
                plt.Line2D([0], [0], marker='o', color='w',
                          markerfacecolor=self.color_scheme['intermediates'],
                          markersize=12, label='Intermediates'),
                plt.Line2D([0], [0], color='green', linewidth=3, label='Low barrier'),
                plt.Line2D([0], [0], color='orange', linewidth=2, label='Medium barrier'),
                plt.Line2D([0], [0], color='red', linewidth=1, label='High barrier')
            ]
            
            ax.legend(handles=legend_elements, loc='upper right')
            
            # Save or show
            if save_path:
                plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            else:
                plt.show()
            
            plt.close()
            
        except Exception as e:
            warnings.warn(f"Network visualization failed: {e}")
    
    def visualize_molecular_structures(self, pathway: PathwayResult,
                                     indices: Optional[List[int]] = None,
                                     save_dir: Optional[str] = None) -> None:
        """
        Visualize molecular structures from pathway.
        
        Args:
            pathway: Reaction pathway
            indices: Specific image indices to visualize
            save_dir: Directory to save structure files
        """
        
        print("🔬 Visualizing molecular structures...")
        
        if not ASE_VIZ_AVAILABLE:
            warnings.warn("ASE visualization not available")
            return
        
        # Select images to visualize
        if indices is None:
            indices = [0, pathway.transition_state_index, -1]  # Reactants, TS, Products
        
        # Create output directory
        if save_dir:
            save_path = Path(save_dir)
            save_path.mkdir(exist_ok=True)
        
        for i in indices:
            if i < len(pathway.images):
                atoms = pathway.images[i]
                
                # Determine structure type
                if i == 0:
                    structure_type = "reactants"
                elif i == pathway.transition_state_index:
                    structure_type = "transition_state"
                elif i == len(pathway.images) - 1:
                    structure_type = "products"
                else:
                    structure_type = f"intermediate_{i}"
                
                # Save structure file
                if save_dir:
                    filename = save_path / f"{structure_type}.xyz"
                    write(str(filename), atoms)
                    print(f"Saved structure: {filename}")
                
                # Visualize (this will open in ASE viewer)
                try:
                    view(atoms)
                except:
                    print(f"Could not open viewer for {structure_type}")
    
    def animate_reaction_pathway(self, pathway: PathwayResult,
                               save_path: Optional[str] = None,
                               interval: int = 500) -> None:
        """
        Create animation of reaction pathway.
        
        Args:
            pathway: Reaction pathway
            save_path: Path to save animation
            interval: Animation interval in milliseconds
        """
        
        print("🎬 Creating pathway animation...")
        
        if not MATPLOTLIB_AVAILABLE:
            warnings.warn("Matplotlib not available for animation")
            return
        
        # This is a simplified 2D animation
        # For 3D molecular animation, you'd need more sophisticated tools
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Energy plot
        x = pathway.reaction_coordinate
        y = pathway.energies - pathway.energies[0]
        
        ax1.plot(x, y, 'o-', color=self.color_scheme['pathway'], alpha=0.3)
        ax1.set_xlabel('Reaction Coordinate (Å)')
        ax1.set_ylabel('Relative Energy (eV)')
        ax1.set_title('Energy Profile')
        
        # Current position marker
        current_point, = ax1.plot([], [], 'ro', markersize=12)
        
        # Structure representation (simplified)
        ax2.set_xlim(-5, 5)
        ax2.set_ylim(-5, 5)
        ax2.set_title('Molecular Structure (Simplified)')
        ax2.set_aspect('equal')
        
        def animate(frame):
            # Update energy plot
            current_point.set_data([x[frame]], [y[frame]])
            
            # Update structure plot (very simplified)
            ax2.clear()
            ax2.set_xlim(-5, 5)
            ax2.set_ylim(-5, 5)
            ax2.set_title(f'Frame {frame + 1}/{len(pathway.images)}')
            
            # Plot atoms as circles (simplified)
            atoms = pathway.images[frame]
            positions = atoms.get_positions()
            
            if len(positions) > 0:
                # Project to 2D (take x, y coordinates)
                x_pos = positions[:, 0]
                y_pos = positions[:, 1]
                
                # Normalize to fit in plot
                if len(x_pos) > 1:
                    x_pos = (x_pos - x_pos.mean()) / (x_pos.std() + 1e-6) * 2
                    y_pos = (y_pos - y_pos.mean()) / (y_pos.std() + 1e-6) * 2
                
                # Plot atoms
                symbols = atoms.get_chemical_symbols()
                colors = {'H': 'white', 'C': 'black', 'O': 'red', 'N': 'blue'}
                
                for i, (x_atom, y_atom, symbol) in enumerate(zip(x_pos, y_pos, symbols)):
                    color = colors.get(symbol, 'gray')
                    ax2.scatter(x_atom, y_atom, c=color, s=200, 
                              edgecolors='black', linewidth=2)
                    ax2.text(x_atom, y_atom + 0.3, symbol, 
                           ha='center', va='center', fontsize=12)
            
            return current_point,
        
        # Create animation
        anim = FuncAnimation(fig, animate, frames=len(pathway.images),
                           interval=interval, blit=False, repeat=True)
        
        # Save or show
        if save_path:
            anim.save(save_path, writer='pillow', fps=2)
            print(f"Animation saved: {save_path}")
        else:
            plt.show()
        
        plt.close()
    
    def create_summary_report(self, pathway: PathwayResult,
                            thermo_data: Optional[ThermoKineticResult],
                            network: Optional[ReactionNetwork],
                            save_path: Optional[str] = None) -> None:
        """
        Create comprehensive summary report with all visualizations.
        
        Args:
            pathway: Reaction pathway
            thermo_data: Thermodynamic data
            network: Reaction network
            save_path: Path to save report
        """
        
        print("📋 Creating summary report...")
        
        if not MATPLOTLIB_AVAILABLE:
            warnings.warn("Matplotlib not available for report generation")
            return
        
        # Create multi-panel figure
        fig = plt.figure(figsize=(20, 12))
        
        # Energy diagram
        ax1 = plt.subplot(2, 3, 1)
        x = pathway.reaction_coordinate
        y = pathway.energies - pathway.energies[0]
        ax1.plot(x, y, 'o-', linewidth=3, markersize=8)
        ax1.set_xlabel('Reaction Coordinate (Å)')
        ax1.set_ylabel('Relative Energy (eV)')
        ax1.set_title('Energy Profile')
        ax1.grid(True, alpha=0.3)
        
        # Thermodynamic summary
        ax2 = plt.subplot(2, 3, 2)
        if thermo_data:
            properties = ['ΔH', 'ΔG', 'ΔS', 'Ea']
            values = [thermo_data.delta_h, thermo_data.delta_g, 
                     thermo_data.delta_s * 1000, thermo_data.activation_energy]  # ΔS in meV/K
            
            bars = ax2.bar(properties, values, color=['blue', 'red', 'green', 'orange'])
            ax2.set_ylabel('Energy (eV) / Entropy (meV/K)')
            ax2.set_title('Thermodynamic Properties')
            
            # Add value labels on bars
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{value:.3f}', ha='center', va='bottom')
        
        # Network visualization (if available)
        if network and network.graph:
            ax3 = plt.subplot(2, 3, 3)
            try:
                import networkx as nx
                pos = nx.spring_layout(network.graph)
                nx.draw(network.graph, pos, ax=ax3, with_labels=True,
                       node_color='lightblue', node_size=500)
                ax3.set_title('Reaction Network')
            except:
                ax3.text(0.5, 0.5, 'Network visualization\nnot available',
                        ha='center', va='center', transform=ax3.transAxes)
        
        # Kinetic properties
        ax4 = plt.subplot(2, 3, 4)
        if thermo_data:
            kinetic_props = ['Rate Constant', 'Half-life', 'Keq']
            kinetic_values = [thermo_data.rate_constant, thermo_data.half_life,
                            thermo_data.equilibrium_constant]
            
            # Use log scale for display
            log_values = [np.log10(max(v, 1e-20)) for v in kinetic_values]
            
            bars = ax4.bar(kinetic_props, log_values, 
                          color=['purple', 'brown', 'cyan'])
            ax4.set_ylabel('log₁₀(Value)')
            ax4.set_title('Kinetic Properties')
            ax4.tick_params(axis='x', rotation=45)
        
        # Summary statistics
        ax5 = plt.subplot(2, 3, 5)
        stats_text = f"""
Pathway Statistics:
• Method: {pathway.method}
• Images: {len(pathway.images)}
• Converged: {pathway.converged}
• Activation Energy: {pathway.activation_energy:.3f} eV
• Reaction Energy: {pathway.reaction_energy:.3f} eV
• TS Position: {pathway.transition_state_index}
"""
        
        if thermo_data:
            stats_text += f"""
Thermodynamics:
• ΔH: {thermo_data.delta_h:.3f} eV
• ΔG: {thermo_data.delta_g:.3f} eV
• Rate: {thermo_data.rate_constant:.2e} s⁻¹
"""
        
        ax5.text(0.1, 0.9, stats_text, transform=ax5.transAxes,
                fontsize=10, verticalalignment='top', fontfamily='monospace')
        ax5.axis('off')
        
        # Calculation details
        ax6 = plt.subplot(2, 3, 6)
        if pathway.calculation_time:
            calc_text = f"""
Calculation Details:
• Time: {pathway.calculation_time:.1f} s
• Iterations: {pathway.n_iterations}
• Force Tolerance: Achieved
"""
        else:
            calc_text = "Calculation details\nnot available"
        
        ax6.text(0.1, 0.9, calc_text, transform=ax6.transAxes,
                fontsize=10, verticalalignment='top', fontfamily='monospace')
        ax6.axis('off')
        
        plt.tight_layout()
        
        # Save or show
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            print(f"Summary report saved: {save_path}")
        else:
            plt.show()
        
        plt.close()


if __name__ == "__main__":
    # Example usage would go here
    print("Visualizer module loaded successfully")
    
    if not MATPLOTLIB_AVAILABLE and not PLOTLY_AVAILABLE:
        print("Warning: No plotting libraries available")
    else:
        print("Visualization capabilities available")
