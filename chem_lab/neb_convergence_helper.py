"""
NEB Convergence Helper

Provides strategies and tools to improve NEB (Nudged Elastic Band) convergence
for reaction pathway calculations. Includes initial guess generation, geometry
pre-optimization, and convergence diagnostics.

Author: AI Chemistry Lab
License: MIT
"""

import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from rdkit import Chem
from rdkit.Chem import AllChem, rdMolDescriptors
import warnings


class NEBConvergenceHelper:
    """
    Helper class to improve NEB calculation convergence through better
    initial guesses and pre-optimization strategies.
    """
    
    def __init__(self):
        """Initialize the NEB convergence helper."""
        self.convergence_thresholds = {
            'energy_change': 1e-6,  # eV
            'force_threshold': 0.05,  # eV/Å
            'max_iterations': 200
        }
        
        self.optimization_settings = {
            'initial_opt_steps': 50,
            'intermediate_opt_steps': 100,
            'final_opt_steps': 200,
            'spring_constant': 0.1,  # eV/Å²
            'climbing_image': True
        }
    
    def generate_improved_initial_guess(self, 
                                      reactant_smiles: List[str], 
                                      product_smiles: List[str],
                                      n_images: int = 8) -> List[Dict[str, Any]]:
        """
        Generate improved initial guess for NEB calculation using chemical intuition.
        
        Args:
            reactant_smiles: List of reactant SMILES
            product_smiles: List of product SMILES  
            n_images: Number of intermediate images
            
        Returns:
            List of geometry dictionaries for NEB pathway
        """
        
        try:
            # Convert SMILES to molecules
            reactant_mols = [Chem.MolFromSmiles(smi) for smi in reactant_smiles]
            product_mols = [Chem.MolFromSmiles(smi) for smi in product_smiles]
            
            # Add hydrogens and generate 3D coordinates
            reactant_mols_3d = []
            for mol in reactant_mols:
                if mol is not None:
                    mol_h = Chem.AddHs(mol)
                    AllChem.EmbedMolecule(mol_h, randomSeed=42)
                    AllChem.MMFFOptimizeMolecule(mol_h)
                    reactant_mols_3d.append(mol_h)
            
            product_mols_3d = []
            for mol in product_mols:
                if mol is not None:
                    mol_h = Chem.AddHs(mol)
                    AllChem.EmbedMolecule(mol_h, randomSeed=42)
                    AllChem.MMFFOptimizeMolecule(mol_h)
                    product_mols_3d.append(mol_h)
            
            # Generate pathway using linear interpolation with chemical constraints
            pathway = self._generate_chemically_aware_pathway(
                reactant_mols_3d, product_mols_3d, n_images
            )
            
            return pathway
            
        except Exception as e:
            warnings.warn(f"Failed to generate improved initial guess: {e}")
            return self._generate_simple_linear_interpolation(
                reactant_smiles, product_smiles, n_images
            )
    
    def _generate_chemically_aware_pathway(self, 
                                         reactants: List[Chem.Mol], 
                                         products: List[Chem.Mol], 
                                         n_images: int) -> List[Dict[str, Any]]:
        """
        Generate pathway with chemical awareness of bond breaking/forming.
        """
        
        pathway = []
        
        # Identify bond changes between reactants and products
        bond_changes = self._identify_bond_changes(reactants, products)
        
        # Create intermediate images with gradual bond changes
        for i in range(n_images):
            alpha = i / (n_images - 1)  # Interpolation parameter (0 to 1)
            
            # Generate intermediate geometry
            intermediate_geom = self._interpolate_with_bond_constraints(
                reactants, products, alpha, bond_changes
            )
            
            pathway.append({
                'geometry': intermediate_geom,
                'alpha': alpha,
                'bond_changes': bond_changes,
                'optimization_level': 'pre_optimized'
            })
        
        return pathway
    
    def _identify_bond_changes(self, reactants: List[Chem.Mol], 
                              products: List[Chem.Mol]) -> Dict[str, Any]:
        """
        Identify bonds that are broken or formed during the reaction.
        """
        
        bond_changes = {
            'bonds_broken': [],
            'bonds_formed': [],
            'bond_order_changes': []
        }
        
        try:
            # Simple heuristic: compare molecular graphs
            # In a real implementation, this would use more sophisticated
            # reaction mapping algorithms

            # For now, return empty changes to avoid errors
            # This would be expanded with proper reaction mapping
            pass

        except Exception as e:
            warnings.warn(f"Bond change identification failed: {e}")
        
        return bond_changes
    
    def _interpolate_with_bond_constraints(self, 
                                         reactants: List[Chem.Mol], 
                                         products: List[Chem.Mol], 
                                         alpha: float,
                                         bond_changes: Dict[str, Any]) -> Dict[str, Any]:
        """
        Interpolate geometries with bond-forming/breaking constraints.
        """
        
        # Simplified implementation - in practice this would:
        # 1. Gradually change bond lengths for breaking/forming bonds
        # 2. Maintain reasonable molecular geometries
        # 3. Use force field constraints to avoid high-energy configurations
        
        geometry = {
            'coordinates': [],
            'atom_types': [],
            'connectivity': [],
            'interpolation_alpha': alpha
        }
        
        return geometry
    
    def _generate_simple_linear_interpolation(self, 
                                            reactant_smiles: List[str], 
                                            product_smiles: List[str], 
                                            n_images: int) -> List[Dict[str, Any]]:
        """
        Fallback: generate simple linear interpolation pathway.
        """
        
        pathway = []
        
        for i in range(n_images):
            alpha = i / (n_images - 1)
            
            pathway.append({
                'geometry': {
                    'interpolation_alpha': alpha,
                    'method': 'linear_interpolation'
                },
                'alpha': alpha,
                'optimization_level': 'unoptimized'
            })
        
        return pathway
    
    def pre_optimize_pathway(self, initial_pathway: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Pre-optimize pathway geometries to improve NEB convergence.
        
        Args:
            initial_pathway: Initial pathway geometries
            
        Returns:
            Pre-optimized pathway geometries
        """
        
        optimized_pathway = []
        
        for i, image in enumerate(initial_pathway):
            # Skip optimization for endpoints (reactants and products)
            if i == 0 or i == len(initial_pathway) - 1:
                optimized_pathway.append(image)
                continue
            
            # Pre-optimize intermediate images
            try:
                optimized_geom = self._optimize_single_image(image['geometry'])
                
                optimized_image = image.copy()
                optimized_image['geometry'] = optimized_geom
                optimized_image['optimization_level'] = 'pre_optimized'
                
                optimized_pathway.append(optimized_image)
                
            except Exception as e:
                warnings.warn(f"Pre-optimization failed for image {i}: {e}")
                optimized_pathway.append(image)
        
        return optimized_pathway
    
    def _optimize_single_image(self, geometry: Dict[str, Any]) -> Dict[str, Any]:
        """
        Optimize a single image geometry using force field methods.
        """
        
        # Placeholder for single image optimization
        # In practice, this would use RDKit MMFF or UFF optimization
        
        optimized_geom = geometry.copy()
        optimized_geom['optimization_status'] = 'optimized'
        optimized_geom['energy_estimate'] = 0.0  # Placeholder
        
        return optimized_geom
    
    def diagnose_convergence_issues(self, neb_result: Any) -> Dict[str, Any]:
        """
        Diagnose common NEB convergence issues and suggest solutions.
        
        Args:
            neb_result: NEB calculation result object
            
        Returns:
            Diagnostic report with issues and recommendations
        """
        
        diagnosis = {
            'convergence_status': getattr(neb_result, 'converged', False),
            'identified_issues': [],
            'recommendations': [],
            'severity': 'unknown'
        }
        
        # Check for common convergence issues
        if not getattr(neb_result, 'converged', True):
            diagnosis['identified_issues'].append('SCF convergence failure')
            diagnosis['recommendations'].extend([
                'Improve initial geometry guess',
                'Reduce spring constant',
                'Increase SCF convergence criteria',
                'Use different exchange-correlation functional'
            ])
        
        # Check activation energy magnitude
        activation_energy = getattr(neb_result, 'activation_energy', None)
        if activation_energy and activation_energy > 8.7:  # > 200 kcal/mol in eV
            diagnosis['identified_issues'].append('Unrealistically high activation energy')
            diagnosis['recommendations'].extend([
                'Check transition state geometry',
                'Verify reaction pathway',
                'Consider alternative mechanism',
                'Use climbing image NEB'
            ])
        
        # Check for oscillating energies
        if hasattr(neb_result, 'energy_profile'):
            energies = getattr(neb_result, 'energy_profile', [])
            if len(energies) > 3 and self._detect_oscillations(energies):
                diagnosis['identified_issues'].append('Oscillating energy profile')
                diagnosis['recommendations'].extend([
                    'Reduce optimization step size',
                    'Increase number of images',
                    'Use adaptive spring constants'
                ])
        
        # Determine severity
        if len(diagnosis['identified_issues']) == 0:
            diagnosis['severity'] = 'none'
        elif len(diagnosis['identified_issues']) <= 2:
            diagnosis['severity'] = 'moderate'
        else:
            diagnosis['severity'] = 'severe'
        
        return diagnosis
    
    def _detect_oscillations(self, energies: List[float]) -> bool:
        """Detect oscillating behavior in energy profile."""
        
        if len(energies) < 4:
            return False
        
        # Simple oscillation detection: check for alternating increases/decreases
        changes = [energies[i+1] - energies[i] for i in range(len(energies)-1)]
        sign_changes = sum(1 for i in range(len(changes)-1) 
                          if changes[i] * changes[i+1] < 0)
        
        # If more than half the changes are sign changes, likely oscillating
        return sign_changes > len(changes) * 0.5
    
    def suggest_neb_parameters(self, reaction_type: str = "general") -> Dict[str, Any]:
        """
        Suggest optimal NEB parameters based on reaction type.
        
        Args:
            reaction_type: Type of reaction (e.g., "esterification", "substitution")
            
        Returns:
            Recommended NEB parameters
        """
        
        base_params = {
            'n_images': 8,
            'spring_constant': 0.1,
            'max_iterations': 200,
            'force_threshold': 0.05,
            'energy_threshold': 1e-6,
            'climbing_image': True,
            'optimization_method': 'BFGS'
        }
        
        # Adjust parameters based on reaction type
        if reaction_type == "esterification":
            base_params.update({
                'n_images': 10,  # More images for complex mechanism
                'spring_constant': 0.05,  # Softer springs
                'max_iterations': 300
            })
        elif reaction_type == "substitution":
            base_params.update({
                'n_images': 6,  # Fewer images for simpler mechanism
                'spring_constant': 0.15,  # Stiffer springs
                'climbing_image': True
            })
        
        return base_params
